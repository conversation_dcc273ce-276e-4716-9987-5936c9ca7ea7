{"test_summary": {"test_id": "integrated_test_20250730_202200", "duration_seconds": 20.340573, "tests_run": 8, "tests_passed": 7, "tests_failed": 1, "success_rate_percent": 87.5, "overall_status": "FAILED"}, "pipeline_coverage": {"stages_tested": ["setup_initialization", "data_fetching_integration", "data_transformation_bridge", "multi_system_storage", "cross_system_validation", "error_recovery_checkpoints", "end_to_end_integration", "performance_scalability"], "total_cases_processed": 1}, "validation_results": {"cross_system_validation": {"score": 0.75, "passed": false, "issues": ["Gcs: Expected ~1, got 0 (score: 0.0%)"]}}, "performance_metrics": {"total_processing_time": 0.101276, "cases_per_second": 9.874007662229946, "memory_usage": "Not measured", "api_calls_made": "Not measured"}, "recommendations": ["Review failed tests and address underlying issues", "Consider testing with more cases to validate scalability", "Address validation issues in cross_system_validation: ['Gcs: Expected ~1, got 0 (score: 0.0%)']"]}