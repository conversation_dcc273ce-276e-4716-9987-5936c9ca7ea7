{"test_summary": {"test_id": "integrated_test_20250730_214158", "duration_seconds": 23.955056, "tests_run": 8, "tests_passed": 8, "tests_failed": 0, "success_rate_percent": 100.0, "overall_status": "PASSED"}, "pipeline_coverage": {"stages_tested": ["setup_initialization", "data_fetching_integration", "data_transformation_bridge", "multi_system_storage", "cross_system_validation", "error_recovery_checkpoints", "end_to_end_integration", "performance_scalability"], "total_cases_processed": 1}, "validation_results": {"cross_system_validation": {"score": 1.0, "passed": true, "issues": []}}, "performance_metrics": {"total_processing_time": 0.10124, "cases_per_second": 9.877518767285657, "memory_usage": "Not measured", "api_calls_made": "Not measured"}, "recommendations": ["Consider testing with more cases to validate scalability"]}