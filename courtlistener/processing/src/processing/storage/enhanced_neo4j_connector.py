"""
Enhanced Neo4j Connector with GraphRAG Integration
Extends the base Neo4j connector with GraphRAG-specific operations, 
idempotent operations, and better error handling.

Key Features:
- GraphRAG entity and relationship management
- Idempotent upsert operations
- Global UID integration
- Schema evolution support
- Enhanced error handling and rollback
"""

import logging
from typing import Dict, List, Optional, Union, Any, Tuple, Set
from datetime import datetime, timezone
import json
import uuid

from .neo4j_connector import Neo4jConnector
from neo4j import GraphDatabase, Result
from neo4j.exceptions import ServiceUnavailable, AuthError, DatabaseError

# Configure logging
logger = logging.getLogger(__name__)

class EnhancedNeo4jConnector(Neo4jConnector):
    """
    Enhanced Neo4j connector with GraphRAG integration and advanced features
    """
    
    def __init__(self, uri: Optional[str] = None, username: Optional[str] = None, 
                 password: Optional[str] = None, enable_graphrag: bool = True):
        """
        Initialize enhanced Neo4j connector
        
        Args:
            uri: Neo4j URI
            username: Neo4j username  
            password: Neo4j password
            enable_graphrag: Enable GraphRAG-specific features
        """
        super().__init__(uri, username, password)
        self.enable_graphrag = enable_graphrag
        
        # Initialize GraphRAG-specific schema
        if enable_graphrag:
            self._initialize_graphrag_schema()
        
        # Operation tracking for idempotency
        self.operation_cache: Dict[str, Any] = {}
        
        logger.info(f"Enhanced Neo4j connector initialized (GraphRAG: {enable_graphrag})")
    
    def _initialize_graphrag_schema(self):
        """Initialize GraphRAG-specific constraints and indexes"""
        with self.driver.session() as session:
            try:
                # GraphRAG entity constraints
                session.run("""
                    CREATE CONSTRAINT graphrag_entity_global_uid IF NOT EXISTS
                    FOR (e:GraphRAGEntity) REQUIRE e.global_uid IS UNIQUE
                """)
                
                session.run("""
                    CREATE CONSTRAINT graphrag_relationship_global_uid IF NOT EXISTS
                    FOR (r:GraphRAGRelationship) REQUIRE r.global_uid IS UNIQUE
                """)
                
                # Practice area and entity type indexes
                session.run("""
                    CREATE INDEX graphrag_entity_practice_area IF NOT EXISTS
                    FOR (e:GraphRAGEntity) ON (e.practice_area)
                """)
                
                session.run("""
                    CREATE INDEX graphrag_entity_type IF NOT EXISTS
                    FOR (e:GraphRAGEntity) ON (e.entity_type)
                """)
                
                session.run("""
                    CREATE INDEX graphrag_entity_jurisdiction IF NOT EXISTS
                    FOR (e:GraphRAGEntity) ON (e.jurisdiction)
                """)
                
                # Full-text search indexes for entity names
                session.run("""
                    CREATE FULLTEXT INDEX graphrag_entity_name_search IF NOT EXISTS
                    FOR (e:GraphRAGEntity) ON EACH [e.name, e.entity_name]
                """)
                
                logger.info("GraphRAG schema initialized in Neo4j")
                
            except Exception as e:
                logger.error(f"Failed to initialize GraphRAG schema: {e}")
    
    # === GraphRAG Entity Operations ===
    
    def create_graphrag_entity(self, global_uid: str, entity_data: Dict[str, Any], 
                              practice_area: str, jurisdiction: str) -> bool:
        """
        Create or update a GraphRAG entity node with idempotent operation
        
        Args:
            global_uid: Global unique identifier
            entity_data: Entity data including type, name, properties
            practice_area: Practice area classification
            jurisdiction: Jurisdiction code
            
        Returns:
            Success flag
        """
        # Check operation cache for idempotency
        cache_key = f"create_entity_{global_uid}"
        if cache_key in self.operation_cache:
            logger.debug(f"Entity {global_uid} already processed (cached)")
            return True
        
        with self.driver.session() as session:
            try:
                # Prepare entity properties
                entity_type = entity_data.get('type', 'Entity')
                entity_name = entity_data.get('name', entity_data.get('entity_name', ''))
                properties = entity_data.get('properties', {})
                
                # Create comprehensive property set
                node_properties = {
                    'global_uid': global_uid,
                    'entity_type': entity_type,
                    'name': entity_name,
                    'entity_name': entity_name,  # Alternative field name
                    'practice_area': practice_area,
                    'jurisdiction': jurisdiction,
                    'created_at': datetime.now(timezone.utc).isoformat(),
                    'updated_at': datetime.now(timezone.utc).isoformat(),
                    'source_system': 'graphrag'
                }
                
                # Add custom properties (filter out complex types)
                for key, value in properties.items():
                    if isinstance(value, (str, int, float, bool)) or value is None:
                        node_properties[key] = value
                    elif isinstance(value, (list, dict)):
                        # Store complex types as JSON strings
                        node_properties[f"{key}_json"] = json.dumps(value)
                
                # Create or update entity with multiple labels
                labels = ['GraphRAGEntity', entity_type.replace(' ', '_')]
                if practice_area:
                    labels.append(practice_area.replace(' ', '_').title())
                
                label_string = ':'.join(labels)
                
                query = f"""
                MERGE (e:{label_string} {{global_uid: $global_uid}})
                ON CREATE SET e += $properties, e.created_at = datetime()
                ON MATCH SET e += $properties, e.updated_at = datetime()
                RETURN e.global_uid as uid, e.entity_type as type
                """
                
                result = session.run(query, global_uid=global_uid, properties=node_properties)
                record = result.single()
                
                if record:
                    # Cache successful operation
                    self.operation_cache[cache_key] = {
                        'timestamp': datetime.now(timezone.utc),
                        'result': {'uid': record['uid'], 'type': record['type']}
                    }
                    
                    logger.debug(f"Created/updated GraphRAG entity {global_uid} ({entity_type})")
                    return True
                
                return False
                
            except Exception as e:
                logger.error(f"Failed to create GraphRAG entity {global_uid}: {e}")
                return False
    
    def create_graphrag_relationship(self, global_uid: str, relationship_data: Dict[str, Any],
                                    source_uid: str, target_uid: str) -> bool:
        """
        Create or update a GraphRAG relationship with idempotent operation
        
        Args:
            global_uid: Global unique identifier for relationship
            relationship_data: Relationship data including type and properties
            source_uid: Source entity global UID
            target_uid: Target entity global UID
            
        Returns:
            Success flag
        """
        # Check operation cache for idempotency
        cache_key = f"create_relationship_{global_uid}"
        if cache_key in self.operation_cache:
            logger.debug(f"Relationship {global_uid} already processed (cached)")
            return True
        
        with self.driver.session() as session:
            try:
                relationship_type = relationship_data.get('type', 'RELATED_TO')
                properties = relationship_data.get('properties', {})
                
                # Prepare relationship properties
                rel_properties = {
                    'global_uid': global_uid,
                    'relationship_type': relationship_type,
                    'created_at': datetime.now(timezone.utc).isoformat(),
                    'updated_at': datetime.now(timezone.utc).isoformat(),
                    'source_system': 'graphrag'
                }
                
                # Add custom properties
                for key, value in properties.items():
                    if isinstance(value, (str, int, float, bool)) or value is None:
                        rel_properties[key] = value
                    elif isinstance(value, (list, dict)):
                        rel_properties[f"{key}_json"] = json.dumps(value)
                
                # Create relationship between existing entities
                query = f"""
                MATCH (source:GraphRAGEntity {{global_uid: $source_uid}})
                MATCH (target:GraphRAGEntity {{global_uid: $target_uid}})
                MERGE (source)-[r:{relationship_type} {{global_uid: $global_uid}}]->(target)
                ON CREATE SET r += $properties, r.created_at = datetime()
                ON MATCH SET r += $properties, r.updated_at = datetime()
                RETURN r.global_uid as uid, type(r) as rel_type
                """
                
                result = session.run(query, 
                                   source_uid=source_uid, 
                                   target_uid=target_uid,
                                   global_uid=global_uid,
                                   properties=rel_properties)
                
                record = result.single()
                
                if record:
                    # Cache successful operation
                    self.operation_cache[cache_key] = {
                        'timestamp': datetime.now(timezone.utc),
                        'result': {'uid': record['uid'], 'type': record['rel_type']}
                    }
                    
                    logger.debug(f"Created/updated GraphRAG relationship {global_uid} ({relationship_type})")
                    return True
                else:
                    logger.warning(f"Could not create relationship {global_uid}: source or target entities not found")
                    return False
                
            except Exception as e:
                logger.error(f"Failed to create GraphRAG relationship {global_uid}: {e}")
                return False
    
    # === Batch Operations ===
    
    def create_graphrag_entities_batch(self, entities: List[Dict[str, Any]], 
                                      practice_area: str, jurisdiction: str) -> Dict[str, Any]:
        """
        Create multiple GraphRAG entities in batch with transaction safety
        
        Args:
            entities: List of entity dictionaries with global_uid and data
            practice_area: Practice area classification
            jurisdiction: Jurisdiction code
            
        Returns:
            Batch operation results
        """
        results = {
            'total': len(entities),
            'created': 0,
            'updated': 0,
            'failed': 0,
            'errors': []
        }
        
        # Process in batches to avoid memory issues
        batch_size = 100
        
        for i in range(0, len(entities), batch_size):
            batch = entities[i:i+batch_size]
            
            with self.driver.session() as session:
                with session.begin_transaction() as tx:
                    try:
                        for entity in batch:
                            global_uid = entity.get('global_uid')
                            entity_data = entity.get('data', entity)
                            
                            if not global_uid:
                                results['failed'] += 1
                                results['errors'].append(f"Missing global_uid for entity: {entity.get('name', 'unknown')}")
                                continue
                            
                            # Check if entity exists
                            check_result = tx.run(
                                "MATCH (e:GraphRAGEntity {global_uid: $uid}) RETURN e.global_uid as uid",
                                uid=global_uid
                            )
                            
                            exists = check_result.single() is not None
                            
                            # Prepare properties
                            entity_type = entity_data.get('type', 'Entity')
                            entity_name = entity_data.get('name', entity_data.get('entity_name', ''))
                            properties = entity_data.get('properties', {})
                            
                            node_properties = {
                                'global_uid': global_uid,
                                'entity_type': entity_type,
                                'name': entity_name,
                                'entity_name': entity_name,
                                'practice_area': practice_area,
                                'jurisdiction': jurisdiction,
                                'source_system': 'graphrag'
                            }
                            
                            # Add timestamp
                            if exists:
                                node_properties['updated_at'] = datetime.now(timezone.utc).isoformat()
                            else:
                                node_properties['created_at'] = datetime.now(timezone.utc).isoformat()
                                node_properties['updated_at'] = datetime.now(timezone.utc).isoformat()
                            
                            # Add custom properties
                            for key, value in properties.items():
                                if isinstance(value, (str, int, float, bool)) or value is None:
                                    node_properties[key] = value
                                elif isinstance(value, (list, dict)):
                                    node_properties[f"{key}_json"] = json.dumps(value)
                            
                            # Create/update entity
                            labels = ['GraphRAGEntity', entity_type.replace(' ', '_')]
                            if practice_area:
                                labels.append(practice_area.replace(' ', '_').title())
                            
                            label_string = ':'.join(labels)
                            
                            tx.run(f"""
                                MERGE (e:{label_string} {{global_uid: $global_uid}})
                                ON CREATE SET e += $properties, e.created_at = datetime()
                                ON MATCH SET e += $properties, e.updated_at = datetime()
                            """, global_uid=global_uid, properties=node_properties)
                            
                            if exists:
                                results['updated'] += 1
                            else:
                                results['created'] += 1
                        
                        # Commit transaction
                        tx.commit()
                        
                    except Exception as e:
                        results['failed'] += len(batch)
                        results['errors'].append(f"Batch failed: {str(e)}")
                        logger.error(f"Batch entity creation failed: {e}")
                        # Transaction will automatically rollback
        
        logger.info(f"Batch entity creation: {results['created']} created, {results['updated']} updated, {results['failed']} failed")
        return results
    
    def create_graphrag_relationships_batch(self, relationships: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create multiple GraphRAG relationships in batch with transaction safety
        
        Args:
            relationships: List of relationship dictionaries with global_uid, source_uid, target_uid, and data
            
        Returns:
            Batch operation results
        """
        results = {
            'total': len(relationships),
            'created': 0,
            'updated': 0,
            'failed': 0,
            'errors': []
        }
        
        # Process in batches
        batch_size = 50  # Smaller for relationships due to complexity
        
        for i in range(0, len(relationships), batch_size):
            batch = relationships[i:i+batch_size]
            
            with self.driver.session() as session:
                with session.begin_transaction() as tx:
                    try:
                        for relationship in batch:
                            global_uid = relationship.get('global_uid')
                            source_uid = relationship.get('source_uid')
                            target_uid = relationship.get('target_uid')
                            rel_data = relationship.get('data', relationship)
                            
                            if not all([global_uid, source_uid, target_uid]):
                                results['failed'] += 1
                                results['errors'].append(f"Missing required UIDs for relationship: {global_uid}")
                                continue
                            
                            # Check if relationship exists
                            check_result = tx.run("""
                                MATCH ()-[r {global_uid: $uid}]->()
                                RETURN r.global_uid as uid
                            """, uid=global_uid)
                            
                            exists = check_result.single() is not None
                            
                            # Prepare relationship properties
                            relationship_type = rel_data.get('type', 'RELATED_TO')
                            properties = rel_data.get('properties', {})
                            
                            rel_properties = {
                                'global_uid': global_uid,
                                'relationship_type': relationship_type,
                                'source_system': 'graphrag'
                            }
                            
                            # Add timestamp
                            if exists:
                                rel_properties['updated_at'] = datetime.now(timezone.utc).isoformat()
                            else:
                                rel_properties['created_at'] = datetime.now(timezone.utc).isoformat()
                                rel_properties['updated_at'] = datetime.now(timezone.utc).isoformat()
                            
                            # Add custom properties
                            for key, value in properties.items():
                                if isinstance(value, (str, int, float, bool)) or value is None:
                                    rel_properties[key] = value
                                elif isinstance(value, (list, dict)):
                                    rel_properties[f"{key}_json"] = json.dumps(value)
                            
                            # Create/update relationship
                            tx.run(f"""
                                MATCH (source:GraphRAGEntity {{global_uid: $source_uid}})
                                MATCH (target:GraphRAGEntity {{global_uid: $target_uid}})
                                MERGE (source)-[r:{relationship_type} {{global_uid: $global_uid}}]->(target)
                                ON CREATE SET r += $properties, r.created_at = datetime()
                                ON MATCH SET r += $properties, r.updated_at = datetime()
                            """, source_uid=source_uid, target_uid=target_uid, 
                                 global_uid=global_uid, properties=rel_properties)
                            
                            if exists:
                                results['updated'] += 1
                            else:
                                results['created'] += 1
                        
                        # Commit transaction
                        tx.commit()
                        
                    except Exception as e:
                        results['failed'] += len(batch)
                        results['errors'].append(f"Batch failed: {str(e)}")
                        logger.error(f"Batch relationship creation failed: {e}")
                        # Transaction will automatically rollback
        
        logger.info(f"Batch relationship creation: {results['created']} created, {results['updated']} updated, {results['failed']} failed")
        return results
    
    # === Query and Retrieval Operations ===
    
    def get_graphrag_entity(self, global_uid: str) -> Optional[Dict[str, Any]]:
        """Retrieve GraphRAG entity by global UID"""
        with self.driver.session() as session:
            try:
                result = session.run("""
                    MATCH (e:GraphRAGEntity {global_uid: $uid})
                    RETURN e
                """, uid=global_uid)
                
                record = result.single()
                if record:
                    return dict(record['e'])
                
                return None
                
            except Exception as e:
                logger.error(f"Failed to retrieve GraphRAG entity {global_uid}: {e}")
                return None
    
    def get_graphrag_entities_by_practice_area(self, practice_area: str, 
                                              entity_type: Optional[str] = None,
                                              limit: int = 100) -> List[Dict[str, Any]]:
        """Get GraphRAG entities filtered by practice area and optionally entity type"""
        with self.driver.session() as session:
            try:
                query = """
                MATCH (e:GraphRAGEntity {practice_area: $practice_area})
                """
                params = {'practice_area': practice_area}
                
                if entity_type:
                    query += " WHERE e.entity_type = $entity_type"
                    params['entity_type'] = entity_type
                
                query += " RETURN e ORDER BY e.created_at DESC LIMIT $limit"
                params['limit'] = limit
                
                result = session.run(query, **params)
                
                return [dict(record['e']) for record in result]
                
            except Exception as e:
                logger.error(f"Failed to get entities by practice area {practice_area}: {e}")
                return []
    
    def get_graphrag_entity_relationships(self, global_uid: str, 
                                         direction: str = "both") -> Dict[str, List[Dict[str, Any]]]:
        """Get all relationships for a GraphRAG entity"""
        with self.driver.session() as session:
            try:
                relationships = {'outgoing': [], 'incoming': []}
                
                if direction in ['outgoing', 'both']:
                    # Outgoing relationships
                    result = session.run("""
                        MATCH (e:GraphRAGEntity {global_uid: $uid})-[r]->(target)
                        RETURN r, target
                        ORDER BY r.created_at DESC
                    """, uid=global_uid)
                    
                    for record in result:
                        relationships['outgoing'].append({
                            'relationship': dict(record['r']),
                            'target': dict(record['target'])
                        })
                
                if direction in ['incoming', 'both']:
                    # Incoming relationships
                    result = session.run("""
                        MATCH (source)-[r]->(e:GraphRAGEntity {global_uid: $uid})
                        RETURN r, source
                        ORDER BY r.created_at DESC
                    """, uid=global_uid)
                    
                    for record in result:
                        relationships['incoming'].append({
                            'relationship': dict(record['r']),
                            'source': dict(record['source'])
                        })
                
                return relationships
                
            except Exception as e:
                logger.error(f"Failed to get relationships for entity {global_uid}: {e}")
                return {'outgoing': [], 'incoming': []}
    
    # === Validation and Integrity ===
    
    def validate_graphrag_entity_integrity(self, global_uid: str) -> Dict[str, Any]:
        """Validate integrity of a GraphRAG entity and its relationships"""
        validation_report = {
            'global_uid': global_uid,
            'exists': False,
            'has_required_properties': False,
            'relationship_count': 0,
            'issues': []
        }
        
        with self.driver.session() as session:
            try:
                # Check if entity exists
                result = session.run("""
                    MATCH (e:GraphRAGEntity {global_uid: $uid})
                    RETURN e, 
                           size((e)-[]-()) as relationship_count
                """, uid=global_uid)
                
                record = result.single()
                if not record:
                    validation_report['issues'].append("Entity not found")
                    return validation_report
                
                validation_report['exists'] = True
                validation_report['relationship_count'] = record['relationship_count']
                
                entity = dict(record['e'])
                
                # Check required properties
                required_props = ['entity_type', 'practice_area', 'jurisdiction', 'created_at']
                missing_props = [prop for prop in required_props if prop not in entity or not entity[prop]]
                
                if missing_props:
                    validation_report['issues'].append(f"Missing required properties: {', '.join(missing_props)}")
                else:
                    validation_report['has_required_properties'] = True
                
                # Check for orphaned entity (no relationships)
                if validation_report['relationship_count'] == 0:
                    validation_report['issues'].append("Entity has no relationships (orphaned)")
                
                return validation_report
                
            except Exception as e:
                logger.error(f"Failed to validate entity integrity {global_uid}: {e}")
                validation_report['issues'].append(f"Validation error: {str(e)}")
                return validation_report
    
    # === Cleanup and Maintenance ===
    
    def cleanup_orphaned_entities(self, practice_area: Optional[str] = None) -> Dict[str, int]:
        """Clean up orphaned GraphRAG entities (entities with no relationships)"""
        with self.driver.session() as session:
            try:
                query = """
                MATCH (e:GraphRAGEntity)
                WHERE size((e)-[]-()) = 0
                """
                
                if practice_area:
                    query += " AND e.practice_area = $practice_area"
                
                query += " DELETE e RETURN COUNT(e) as deleted_count"
                
                params = {'practice_area': practice_area} if practice_area else {}
                result = session.run(query, **params)
                
                deleted_count = result.single()['deleted_count']
                
                logger.info(f"Cleaned up {deleted_count} orphaned entities")
                return {'deleted_count': deleted_count}
                
            except Exception as e:
                logger.error(f"Failed to cleanup orphaned entities: {e}")
                return {'deleted_count': 0, 'error': str(e)}
    
    def get_graphrag_statistics(self) -> Dict[str, Any]:
        """Get comprehensive GraphRAG statistics"""
        with self.driver.session() as session:
            try:
                # Entity statistics
                entity_stats = session.run("""
                    MATCH (e:GraphRAGEntity)
                    RETURN 
                        COUNT(e) as total_entities,
                        COUNT(DISTINCT e.practice_area) as practice_areas,
                        COUNT(DISTINCT e.entity_type) as entity_types,
                        COUNT(DISTINCT e.jurisdiction) as jurisdictions
                """).single()
                
                # Relationship statistics
                rel_stats = session.run("""
                    MATCH ()-[r]->()
                    WHERE r.source_system = 'graphrag'
                    RETURN 
                        COUNT(r) as total_relationships,
                        COUNT(DISTINCT type(r)) as relationship_types
                """).single()
                
                # Practice area breakdown
                practice_area_stats = session.run("""
                    MATCH (e:GraphRAGEntity)
                    RETURN e.practice_area as practice_area, COUNT(e) as count
                    ORDER BY count DESC
                """)
                
                practice_areas = {record['practice_area']: record['count'] 
                                for record in practice_area_stats}
                
                return {
                    'entities': dict(entity_stats),
                    'relationships': dict(rel_stats),
                    'practice_areas': practice_areas,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
                
            except Exception as e:
                logger.error(f"Failed to get GraphRAG statistics: {e}")
                return {'error': str(e)}
    
    # === Connection Management ===
    
    def test_connection(self) -> Dict[str, Any]:
        """Test Neo4j connection and return status"""
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                record = result.single()
                
                if record and record['test'] == 1:
                    return {
                        'status': 'connected',
                        'timestamp': datetime.now(timezone.utc).isoformat(),
                        'server_info': self.driver.get_server_info()
                    }
                else:
                    return {
                        'status': 'error',
                        'message': 'Unexpected response from Neo4j'
                    }
        
        except ServiceUnavailable as e:
            return {
                'status': 'unavailable',
                'message': f'Neo4j service unavailable: {str(e)}'
            }
        except AuthError as e:
            return {
                'status': 'auth_error', 
                'message': f'Authentication failed: {str(e)}'
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Connection test failed: {str(e)}'
            }
    
    def clear_operation_cache(self):
        """Clear the operation cache"""
        self.operation_cache.clear()
        logger.info("Operation cache cleared")
    
    def close(self):
        """Close enhanced Neo4j connector"""
        try:
            self.clear_operation_cache()
            super().close()
            logger.info("Enhanced Neo4j connector closed")
        except Exception as e:
            logger.error(f"Error closing enhanced Neo4j connector: {e}")