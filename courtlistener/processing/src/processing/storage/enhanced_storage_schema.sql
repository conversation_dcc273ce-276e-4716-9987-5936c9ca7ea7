-- Enhanced Storage Orchestrator Schema
-- SQL migration script for GraphRAG storage enhancement tables

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Global UID Registry Table
-- Tracks entities across all storage backends with global unique identifiers
CREATE TABLE IF NOT EXISTS global_uid_registry (
    uid TEXT PRIMARY KEY,
    entity_type TEXT NOT NULL,
    source_system TEXT NOT NULL,
    source_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    -- Backend-specific reference tracking
    gcs_paths TEXT[] DEFAULT '{}',
    supabase_ids TEXT[] DEFAULT '{}',
    neo4j_ids TEXT[] DEFAULT '{}',
    pinecone_ids TEXT[] DEFAULT '{}',
    
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'deleted', 'archived')),
    
    -- Indexes for performance
    UNIQUE(source_system, source_id, entity_type)
);

-- Indexes for global_uid_registry
CREATE INDEX IF NOT EXISTS idx_global_uid_entity_type ON global_uid_registry(entity_type);
CREATE INDEX IF NOT EXISTS idx_global_uid_source_system ON global_uid_registry(source_system);
CREATE INDEX IF NOT EXISTS idx_global_uid_created_at ON global_uid_registry(created_at);
CREATE INDEX IF NOT EXISTS idx_global_uid_status ON global_uid_registry(status);
CREATE INDEX IF NOT EXISTS idx_global_uid_metadata ON global_uid_registry USING GIN(metadata);

-- 2. GraphRAG Schemas Table
-- Caches extracted schemas for reuse across processing batches
CREATE TABLE IF NOT EXISTS graphrag_schemas (
    schema_id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    practice_area TEXT NOT NULL,
    schema_version TEXT NOT NULL,
    schema_json JSONB NOT NULL,
    node_types JSONB DEFAULT '[]',
    relationship_types JSONB DEFAULT '[]',
    
    -- Schema metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'deprecated', 'testing')),
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    -- Performance metrics
    avg_processing_time_ms INTEGER,
    success_rate DECIMAL(5,4),
    
    UNIQUE(practice_area, schema_version)
);

-- Indexes for graphrag_schemas
CREATE INDEX IF NOT EXISTS idx_graphrag_schemas_practice_area ON graphrag_schemas(practice_area);
CREATE INDEX IF NOT EXISTS idx_graphrag_schemas_status ON graphrag_schemas(status);
CREATE INDEX IF NOT EXISTS idx_graphrag_schemas_created_at ON graphrag_schemas(created_at);
CREATE INDEX IF NOT EXISTS idx_graphrag_schemas_usage ON graphrag_schemas(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_graphrag_schemas_node_types ON graphrag_schemas USING GIN(node_types);
CREATE INDEX IF NOT EXISTS idx_graphrag_schemas_rel_types ON graphrag_schemas USING GIN(relationship_types);

-- 3. ETL Checkpoints Table
-- Manages processing checkpoints for resuming interrupted operations
CREATE TABLE IF NOT EXISTS etl_checkpoints (
    checkpoint_id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    pipeline_name TEXT NOT NULL,
    batch_id TEXT NOT NULL,
    
    -- Processing state
    global_uids_processed TEXT[] DEFAULT '{}',
    global_uids_failed TEXT[] DEFAULT '{}',
    global_uids_skipped TEXT[] DEFAULT '{}',
    
    -- Checkpoint metadata
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'failed', 'archived')),
    
    -- Progress tracking
    total_items INTEGER DEFAULT 0,
    processed_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    
    -- Performance metrics
    processing_rate_per_minute DECIMAL(10,2),
    estimated_completion_time TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(pipeline_name, batch_id)
);

-- Indexes for etl_checkpoints
CREATE INDEX IF NOT EXISTS idx_etl_checkpoints_pipeline ON etl_checkpoints(pipeline_name);
CREATE INDEX IF NOT EXISTS idx_etl_checkpoints_batch ON etl_checkpoints(batch_id);
CREATE INDEX IF NOT EXISTS idx_etl_checkpoints_status ON etl_checkpoints(status);
CREATE INDEX IF NOT EXISTS idx_etl_checkpoints_timestamp ON etl_checkpoints(timestamp);
CREATE INDEX IF NOT EXISTS idx_etl_checkpoints_metadata ON etl_checkpoints USING GIN(metadata);

-- 4. Storage Operations Table
-- Audit trail and rollback support for multi-backend operations
CREATE TABLE IF NOT EXISTS storage_operations (
    operation_id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    operation_type TEXT NOT NULL CHECK (operation_type IN ('create', 'update', 'delete', 'upsert', 'batch_create', 'batch_update')),
    backend TEXT NOT NULL CHECK (backend IN ('gcs', 'supabase', 'neo4j', 'pinecone')),
    global_uid TEXT NOT NULL,
    
    -- Operation data
    data JSONB NOT NULL,
    metadata JSONB DEFAULT '{}',
    
    -- Status tracking
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'rolled_back')),
    
    -- Error handling
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    
    -- Rollback support
    rollback_data JSONB,
    rollback_timestamp TIMESTAMP WITH TIME ZONE,
    
    -- Performance tracking
    execution_time_ms INTEGER,
    
    FOREIGN KEY (global_uid) REFERENCES global_uid_registry(uid) ON DELETE CASCADE
);

-- Indexes for storage_operations
CREATE INDEX IF NOT EXISTS idx_storage_operations_global_uid ON storage_operations(global_uid);
CREATE INDEX IF NOT EXISTS idx_storage_operations_backend ON storage_operations(backend);
CREATE INDEX IF NOT EXISTS idx_storage_operations_status ON storage_operations(status);
CREATE INDEX IF NOT EXISTS idx_storage_operations_timestamp ON storage_operations(timestamp);
CREATE INDEX IF NOT EXISTS idx_storage_operations_type ON storage_operations(operation_type);

-- 5. GraphRAG Entities Table
-- Stores GraphRAG extracted entities with metadata
CREATE TABLE IF NOT EXISTS graphrag_entities (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    global_uid TEXT NOT NULL,
    entity_type TEXT NOT NULL,
    entity_name TEXT,
    
    -- Context information
    jurisdiction TEXT,
    practice_area TEXT,
    court_id TEXT,
    case_id TEXT,
    
    -- Entity properties
    properties JSONB DEFAULT '{}',
    
    -- Storage references
    gcs_path TEXT,
    neo4j_node_id TEXT,
    pinecone_vector_id TEXT,
    
    -- Processing metadata
    extracted_by TEXT, -- GraphRAG model version
    extraction_confidence DECIMAL(5,4),
    validation_status TEXT DEFAULT 'pending' CHECK (validation_status IN ('pending', 'validated', 'rejected')),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    FOREIGN KEY (global_uid) REFERENCES global_uid_registry(uid) ON DELETE CASCADE
);

-- Indexes for graphrag_entities
CREATE INDEX IF NOT EXISTS idx_graphrag_entities_global_uid ON graphrag_entities(global_uid);
CREATE INDEX IF NOT EXISTS idx_graphrag_entities_type ON graphrag_entities(entity_type);
CREATE INDEX IF NOT EXISTS idx_graphrag_entities_jurisdiction ON graphrag_entities(jurisdiction);
CREATE INDEX IF NOT EXISTS idx_graphrag_entities_practice_area ON graphrag_entities(practice_area);
CREATE INDEX IF NOT EXISTS idx_graphrag_entities_name ON graphrag_entities(entity_name);
CREATE INDEX IF NOT EXISTS idx_graphrag_entities_properties ON graphrag_entities USING GIN(properties);
CREATE INDEX IF NOT EXISTS idx_graphrag_entities_created_at ON graphrag_entities(created_at);

-- 6. GraphRAG Relationships Table
-- Stores GraphRAG extracted relationships between entities
CREATE TABLE IF NOT EXISTS graphrag_relationships (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    global_uid TEXT NOT NULL,
    relationship_type TEXT NOT NULL,
    
    -- Relationship endpoints
    source_uid TEXT NOT NULL,
    target_uid TEXT NOT NULL,
    
    -- Relationship properties
    properties JSONB DEFAULT '{}',
    
    -- Context information
    jurisdiction TEXT,
    practice_area TEXT,
    case_id TEXT,
    
    -- Storage references
    neo4j_relationship_id TEXT,
    
    -- Processing metadata
    extracted_by TEXT, -- GraphRAG model version
    extraction_confidence DECIMAL(5,4),
    validation_status TEXT DEFAULT 'pending' CHECK (validation_status IN ('pending', 'validated', 'rejected')),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    FOREIGN KEY (global_uid) REFERENCES global_uid_registry(uid) ON DELETE CASCADE,
    FOREIGN KEY (source_uid) REFERENCES global_uid_registry(uid) ON DELETE CASCADE,
    FOREIGN KEY (target_uid) REFERENCES global_uid_registry(uid) ON DELETE CASCADE
);

-- Indexes for graphrag_relationships
CREATE INDEX IF NOT EXISTS idx_graphrag_relationships_global_uid ON graphrag_relationships(global_uid);
CREATE INDEX IF NOT EXISTS idx_graphrag_relationships_type ON graphrag_relationships(relationship_type);
CREATE INDEX IF NOT EXISTS idx_graphrag_relationships_source ON graphrag_relationships(source_uid);
CREATE INDEX IF NOT EXISTS idx_graphrag_relationships_target ON graphrag_relationships(target_uid);
CREATE INDEX IF NOT EXISTS idx_graphrag_relationships_jurisdiction ON graphrag_relationships(jurisdiction);
CREATE INDEX IF NOT EXISTS idx_graphrag_relationships_practice_area ON graphrag_relationships(practice_area);
CREATE INDEX IF NOT EXISTS idx_graphrag_relationships_properties ON graphrag_relationships USING GIN(properties);

-- 7. Processing Batches Table (Enhanced version of existing table)
-- Enhanced processing batch tracking with GraphRAG support
CREATE TABLE IF NOT EXISTS processing_batches_enhanced (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    batch_name TEXT NOT NULL,
    pipeline_type TEXT NOT NULL CHECK (pipeline_type IN ('courtlistener', 'graphrag', 'hybrid')),
    
    -- Processing parameters
    source TEXT NOT NULL,
    jurisdiction TEXT NOT NULL,
    practice_area TEXT,
    query_params JSONB DEFAULT '{}',
    
    -- Schema information for GraphRAG batches
    schema_id TEXT,
    schema_version TEXT,
    
    -- Timing
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    estimated_duration_minutes INTEGER,
    
    -- Progress tracking
    total_items INTEGER DEFAULT 0,
    processed_items INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    skipped_count INTEGER DEFAULT 0,
    
    -- Status
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    
    -- Error handling
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- Performance metrics
    items_per_minute DECIMAL(10,2),
    memory_usage_mb INTEGER,
    cost_estimate DECIMAL(10,4),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- User context
    user_id TEXT,
    user_role TEXT,
    tenant_id TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    FOREIGN KEY (schema_id) REFERENCES graphrag_schemas(schema_id) ON DELETE SET NULL
);

-- Indexes for processing_batches_enhanced
CREATE INDEX IF NOT EXISTS idx_processing_batches_enhanced_type ON processing_batches_enhanced(pipeline_type);
CREATE INDEX IF NOT EXISTS idx_processing_batches_enhanced_status ON processing_batches_enhanced(status);
CREATE INDEX IF NOT EXISTS idx_processing_batches_enhanced_jurisdiction ON processing_batches_enhanced(jurisdiction);
CREATE INDEX IF NOT EXISTS idx_processing_batches_enhanced_practice_area ON processing_batches_enhanced(practice_area);
CREATE INDEX IF NOT EXISTS idx_processing_batches_enhanced_start_time ON processing_batches_enhanced(start_time);
CREATE INDEX IF NOT EXISTS idx_processing_batches_enhanced_schema ON processing_batches_enhanced(schema_id);

-- 8. Storage Health Metrics Table
-- Tracks storage system health and performance metrics
CREATE TABLE IF NOT EXISTS storage_health_metrics (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Backend availability
    gcs_available BOOLEAN DEFAULT TRUE,
    supabase_available BOOLEAN DEFAULT TRUE,
    neo4j_available BOOLEAN DEFAULT TRUE,
    pinecone_available BOOLEAN DEFAULT TRUE,
    
    -- Performance metrics
    gcs_response_time_ms INTEGER,
    supabase_response_time_ms INTEGER,
    neo4j_response_time_ms INTEGER,
    pinecone_response_time_ms INTEGER,
    
    -- Storage utilization
    gcs_storage_used_gb DECIMAL(10,2),
    supabase_records_count BIGINT,
    neo4j_nodes_count BIGINT,
    pinecone_vectors_count BIGINT,
    
    -- Error rates
    gcs_error_rate DECIMAL(5,4),
    supabase_error_rate DECIMAL(5,4),
    neo4j_error_rate DECIMAL(5,4),
    pinecone_error_rate DECIMAL(5,4),
    
    -- Global consistency metrics
    global_uid_consistency_score DECIMAL(5,4),
    cross_backend_integrity_score DECIMAL(5,4),
    
    metadata JSONB DEFAULT '{}'
);

-- Index for storage health metrics
CREATE INDEX IF NOT EXISTS idx_storage_health_metrics_timestamp ON storage_health_metrics(timestamp);

-- 9. Create views for common queries

-- View: Active Global UIDs with backend status
CREATE OR REPLACE VIEW v_active_global_uids AS
SELECT 
    uid,
    entity_type,
    source_system,
    source_id,
    created_at,
    array_length(gcs_paths, 1) as gcs_count,
    array_length(supabase_ids, 1) as supabase_count,
    array_length(neo4j_ids, 1) as neo4j_count,
    array_length(pinecone_ids, 1) as pinecone_count,
    metadata
FROM global_uid_registry 
WHERE status = 'active';

-- View: GraphRAG Processing Summary
CREATE OR REPLACE VIEW v_graphrag_processing_summary AS
SELECT 
    pb.id as batch_id,
    pb.batch_name,
    pb.practice_area,
    pb.jurisdiction,
    pb.status,
    pb.total_items,
    pb.processed_items,
    pb.success_count,
    pb.failure_count,
    gs.schema_version,
    COUNT(ge.id) as entities_extracted,
    COUNT(gr.id) as relationships_extracted,
    pb.start_time,
    pb.end_time
FROM processing_batches_enhanced pb
LEFT JOIN graphrag_schemas gs ON pb.schema_id = gs.schema_id
LEFT JOIN graphrag_entities ge ON ge.case_id = pb.id
LEFT JOIN graphrag_relationships gr ON gr.case_id = pb.id
WHERE pb.pipeline_type = 'graphrag'
GROUP BY pb.id, pb.batch_name, pb.practice_area, pb.jurisdiction, 
         pb.status, pb.total_items, pb.processed_items, pb.success_count, 
         pb.failure_count, gs.schema_version, pb.start_time, pb.end_time;

-- View: Storage Integrity Report
CREATE OR REPLACE VIEW v_storage_integrity_report AS
WITH backend_counts AS (
    SELECT 
        uid,
        entity_type,
        CASE WHEN array_length(gcs_paths, 1) > 0 THEN 1 ELSE 0 END as has_gcs,
        CASE WHEN array_length(supabase_ids, 1) > 0 THEN 1 ELSE 0 END as has_supabase,
        CASE WHEN array_length(neo4j_ids, 1) > 0 THEN 1 ELSE 0 END as has_neo4j,
        CASE WHEN array_length(pinecone_ids, 1) > 0 THEN 1 ELSE 0 END as has_pinecone
    FROM global_uid_registry 
    WHERE status = 'active'
)
SELECT 
    uid,
    entity_type,
    (has_gcs + has_supabase + has_neo4j + has_pinecone) as backend_count,
    has_gcs,
    has_supabase,
    has_neo4j,
    has_pinecone,
    CASE 
        WHEN (has_gcs + has_supabase + has_neo4j + has_pinecone) = 4 THEN 'complete'
        WHEN (has_gcs + has_supabase + has_neo4j + has_pinecone) >= 2 THEN 'partial'
        ELSE 'incomplete'
    END as integrity_status
FROM backend_counts;

-- 10. Stored procedures for common operations

-- Function: Get storage statistics
CREATE OR REPLACE FUNCTION get_storage_statistics()
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'timestamp', NOW(),
        'global_uids', (
            SELECT jsonb_object_agg(entity_type, count)
            FROM (
                SELECT entity_type, COUNT(*) as count 
                FROM global_uid_registry 
                WHERE status = 'active'
                GROUP BY entity_type
            ) entity_counts
        ),
        'schemas', (
            SELECT jsonb_object_agg(practice_area, count)
            FROM (
                SELECT practice_area, COUNT(*) as count 
                FROM graphrag_schemas 
                WHERE status = 'active'
                GROUP BY practice_area
            ) schema_counts
        ),
        'checkpoints', (
            SELECT jsonb_object_agg(status, count)
            FROM (
                SELECT status, COUNT(*) as count 
                FROM etl_checkpoints 
                GROUP BY status
            ) checkpoint_counts
        ),
        'integrity', (
            SELECT jsonb_build_object(
                'complete', COUNT(*) FILTER (WHERE integrity_status = 'complete'),
                'partial', COUNT(*) FILTER (WHERE integrity_status = 'partial'),
                'incomplete', COUNT(*) FILTER (WHERE integrity_status = 'incomplete')
            )
            FROM v_storage_integrity_report
        )
    ) INTO result;
    
    RETURN result;
END;
$$;

-- Function: Cleanup old records
CREATE OR REPLACE FUNCTION cleanup_old_records(retention_days INTEGER DEFAULT 30)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    cutoff_date TIMESTAMP WITH TIME ZONE;
    result JSONB;
    deleted_checkpoints INTEGER;
    deleted_operations INTEGER;
    deleted_metrics INTEGER;
BEGIN
    cutoff_date := NOW() - INTERVAL '1 day' * retention_days;
    
    -- Delete old completed checkpoints
    DELETE FROM etl_checkpoints 
    WHERE timestamp < cutoff_date AND status = 'completed';
    GET DIAGNOSTICS deleted_checkpoints = ROW_COUNT;
    
    -- Delete old storage operations
    DELETE FROM storage_operations 
    WHERE timestamp < cutoff_date AND status = 'completed';
    GET DIAGNOSTICS deleted_operations = ROW_COUNT;
    
    -- Delete old health metrics (keep last 7 days always)
    DELETE FROM storage_health_metrics 
    WHERE timestamp < cutoff_date AND timestamp < NOW() - INTERVAL '7 days';
    GET DIAGNOSTICS deleted_metrics = ROW_COUNT;
    
    SELECT jsonb_build_object(
        'deleted_checkpoints', deleted_checkpoints,
        'deleted_operations', deleted_operations,
        'deleted_metrics', deleted_metrics,
        'cutoff_date', cutoff_date
    ) INTO result;
    
    RETURN result;
END;
$$;

-- Add comments for documentation
COMMENT ON TABLE global_uid_registry IS 'Central registry for tracking entities across all storage backends';
COMMENT ON TABLE graphrag_schemas IS 'Cache for GraphRAG extracted schemas by practice area';
COMMENT ON TABLE etl_checkpoints IS 'Checkpoints for resuming interrupted ETL operations';
COMMENT ON TABLE storage_operations IS 'Audit trail for all storage operations with rollback support';
COMMENT ON TABLE graphrag_entities IS 'GraphRAG extracted entities with metadata and storage references';
COMMENT ON TABLE graphrag_relationships IS 'GraphRAG extracted relationships between entities';
COMMENT ON TABLE processing_batches_enhanced IS 'Enhanced processing batch tracking with GraphRAG support';
COMMENT ON TABLE storage_health_metrics IS 'Storage system health and performance metrics';

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO your_app_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO your_app_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO your_app_user;