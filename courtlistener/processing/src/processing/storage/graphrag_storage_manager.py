"""
GraphRAG Storage Manager
Specialized storage manager for Neo4j GraphRAG Python package integration.
Handles GraphRAG schemas, entities, relationships, and coordinates with enhanced storage orchestrator.

Key Features:
- Schema extraction and caching for legal domains
- Entity and relationship storage across all backends
- Practice area-specific schema management
- Integration with Neo4j GraphRAG SimpleKGPipeline
- Idempotent operations with global UID tracking
"""

import json
import logging
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Optional, Union, Any, Tuple
from dataclasses import dataclass
from enum import Enum

# Neo4j GraphRAG imports (when available)
try:
    from neo4j_graphrag.experimental.components.schema import SchemaFromTextExtractor
    from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
    from neo4j_graphrag.llm import LLMInterface
    GRAPHRAG_AVAILABLE = True
except ImportError:
    GRAPHRAG_AVAILABLE = False
    logger.warning("Neo4j GraphRAG package not available. Some features will be disabled.")

from .enhanced_storage_orchestrator import EnhancedStorageOrchestrator, GlobalUID, StorageOperationType
from .supabase_connector import SupabaseConnector
from .neo4j_connector import Neo4jConnector

# Configure logging
logger = logging.getLogger(__name__)

class PracticeArea(Enum):
    """Practice area enumeration for legal domain specialization"""
    PERSONAL_INJURY = "personal_injury"
    CRIMINAL_DEFENSE = "criminal_defense"
    FAMILY_LAW = "family_law"
    ESTATE_PLANNING = "estate_planning"
    IMMIGRATION_LAW = "immigration_law"
    REAL_ESTATE = "real_estate"
    BANKRUPTCY = "bankruptcy"
    GENERAL = "general"

@dataclass
class LegalSchema:
    """Legal domain schema for GraphRAG processing"""
    schema_id: str
    practice_area: PracticeArea
    version: str
    node_types: List[Dict[str, Any]]
    relationship_types: List[Dict[str, Any]]
    extraction_prompt: str
    validation_rules: Dict[str, Any]
    created_at: datetime
    metadata: Dict[str, Any] = None

@dataclass 
class GraphRAGResult:
    """Result from GraphRAG processing"""
    global_uid: str
    entities: List[Dict[str, Any]]
    relationships: List[Dict[str, Any]]
    embeddings: Dict[str, List[float]]
    metadata: Dict[str, Any]
    processing_stats: Dict[str, Any]

class GraphRAGStorageManager:
    """
    Specialized storage manager for GraphRAG operations
    
    Manages GraphRAG schemas, entity extraction, and coordinates storage
    across all backends using the enhanced storage orchestrator.
    """
    
    def __init__(self, storage_orchestrator: Optional[EnhancedStorageOrchestrator] = None):
        """
        Initialize GraphRAG Storage Manager
        
        Args:
            storage_orchestrator: Optional pre-initialized storage orchestrator
        """
        self.orchestrator = storage_orchestrator or EnhancedStorageOrchestrator()
        
        # Practice area-specific configurations
        self.practice_area_configs = self._initialize_practice_area_configs()
        
        # Schema cache
        self.schema_cache: Dict[str, LegalSchema] = {}
        
        # GraphRAG components (initialized when needed)
        self.schema_extractors: Dict[str, Any] = {}
        self.kg_pipelines: Dict[str, Any] = {}
        
        logger.info("GraphRAG Storage Manager initialized")
    
    def _initialize_practice_area_configs(self) -> Dict[PracticeArea, Dict[str, Any]]:
        """Initialize practice area-specific configurations"""
        return {
            PracticeArea.PERSONAL_INJURY: {
                "primary_entities": ["Plaintiff", "Defendant", "Injury", "Medical_Provider", "Insurance_Company", "Damages"],
                "primary_relationships": ["SUFFERED_INJURY", "PROVIDED_TREATMENT", "CAUSED_INJURY", "AWARDED_DAMAGES"],
                "extraction_focus": ["medical entities", "injury descriptions", "damages", "liability"],
                "schema_prompt_additions": "Focus on medical entities, injuries, damages, and liability relationships."
            },
            PracticeArea.CRIMINAL_DEFENSE: {
                "primary_entities": ["Defendant", "Prosecutor", "Crime", "Evidence", "Witness", "Sentence"],
                "primary_relationships": ["CHARGED_WITH", "PROSECUTED_BY", "TESTIFIED_AGAINST", "SENTENCED_TO"],
                "extraction_focus": ["criminal charges", "evidence", "procedural steps", "sentencing"],
                "schema_prompt_additions": "Focus on criminal charges, evidence, legal procedures, and sentencing."
            },
            PracticeArea.FAMILY_LAW: {
                "primary_entities": ["Spouse", "Child", "Custody", "Support", "Property", "Divorce_Decree"],
                "primary_relationships": ["MARRIED_TO", "PARENT_OF", "AWARDED_CUSTODY", "ORDERED_SUPPORT"],
                "extraction_focus": ["family relationships", "custody arrangements", "support orders", "property division"],
                "schema_prompt_additions": "Focus on family relationships, custody, support, and property division."
            },
            PracticeArea.ESTATE_PLANNING: {
                "primary_entities": ["Testator", "Beneficiary", "Will", "Trust", "Asset", "Executor"],
                "primary_relationships": ["CREATED_WILL", "BENEFICIARY_OF", "MANAGES_TRUST", "INHERITS_ASSET"],
                "extraction_focus": ["estate documents", "beneficiaries", "assets", "trust arrangements"],
                "schema_prompt_additions": "Focus on estate planning documents, beneficiaries, assets, and trust relationships."
            },
            PracticeArea.IMMIGRATION_LAW: {
                "primary_entities": ["Applicant", "Immigration_Status", "Visa", "Green_Card", "Deportation_Order"],
                "primary_relationships": ["APPLIED_FOR", "GRANTED_STATUS", "ISSUED_VISA", "ORDERED_DEPORTATION"],
                "extraction_focus": ["immigration status", "applications", "visas", "proceedings"],
                "schema_prompt_additions": "Focus on immigration status, applications, visas, and legal proceedings."
            },
            PracticeArea.REAL_ESTATE: {
                "primary_entities": ["Property", "Buyer", "Seller", "Contract", "Deed", "Mortgage"],
                "primary_relationships": ["PURCHASED_PROPERTY", "SOLD_PROPERTY", "MORTGAGED_BY", "TITLED_TO"],
                "extraction_focus": ["property transactions", "contracts", "financing", "title issues"],
                "schema_prompt_additions": "Focus on property transactions, contracts, financing, and title relationships."
            },
            PracticeArea.BANKRUPTCY: {
                "primary_entities": ["Debtor", "Creditor", "Asset", "Debt", "Discharge", "Trustee"],
                "primary_relationships": ["OWES_DEBT", "DISCHARGED_DEBT", "LIQUIDATED_ASSET", "ADMINISTERED_BY"],
                "extraction_focus": ["debts", "assets", "discharge orders", "trustee actions"],
                "schema_prompt_additions": "Focus on debts, assets, discharge orders, and bankruptcy procedures."
            },
            PracticeArea.GENERAL: {
                "primary_entities": ["Party", "Court", "Judge", "Attorney", "Document", "Ruling"],
                "primary_relationships": ["REPRESENTED_BY", "PRESIDED_OVER", "FILED_DOCUMENT", "ISSUED_RULING"],
                "extraction_focus": ["general legal entities", "court proceedings", "legal documents"],
                "schema_prompt_additions": "Focus on general legal entities and court proceeding relationships."
            }
        }
    
    # === Schema Management ===
    
    def get_legal_schema_prompt(self, practice_area: PracticeArea) -> str:
        """Generate practice area-specific schema extraction prompt"""
        config = self.practice_area_configs.get(practice_area, self.practice_area_configs[PracticeArea.GENERAL])
        
        base_prompt = """
Extract entities and relationships from this legal document focusing on the {practice_area} domain.

REQUIRED ENTITIES TO IDENTIFY:
- Legal Parties (plaintiff, defendant, appellant, etc.)
- Judges and Court Personnel  
- Courts and Jurisdictions
- Legal Concepts (statutes, regulations, legal principles)
- Case Citations and Precedents
- Legal Procedures and Motions

PRACTICE AREA SPECIFIC ENTITIES:
{primary_entities}

REQUIRED RELATIONSHIPS TO EXTRACT:
- PRESIDES_OVER (Judge -> Case)
- CITES_PRECEDENT (Case -> Citation) 
- FILED_IN (Case -> Court)
- REPRESENTED_BY (Party -> Attorney)
- RULES_ON (Judge -> Motion/Issue)
- APPEALS_FROM (Case -> Lower Court Decision)

PRACTICE AREA SPECIFIC RELATIONSHIPS:
{primary_relationships}

EXTRACTION FOCUS:
{extraction_focus}

{schema_prompt_additions}

Document Text: {{text}}
"""
        
        return base_prompt.format(
            practice_area=practice_area.value.replace("_", " ").title(),
            primary_entities=", ".join(config["primary_entities"]),
            primary_relationships=", ".join(config["primary_relationships"]),
            extraction_focus=", ".join(config["extraction_focus"]),
            schema_prompt_additions=config["schema_prompt_additions"]
        )
    
    async def extract_legal_schema(self, practice_area: PracticeArea, sample_text: str, 
                                  llm: Optional[Any] = None) -> Optional[LegalSchema]:
        """
        Extract legal schema from sample text using GraphRAG
        
        Args:
            practice_area: Practice area for specialization
            sample_text: Sample legal text for schema extraction
            llm: LLM interface for extraction
            
        Returns:
            Extracted legal schema or None if failed
        """
        if not GRAPHRAG_AVAILABLE:
            logger.error("Neo4j GraphRAG not available for schema extraction")
            return None
        
        try:
            # Get or create schema extractor
            extractor_key = practice_area.value
            if extractor_key not in self.schema_extractors:
                if not llm:
                    logger.error("LLM interface required for schema extraction")
                    return None
                
                prompt_template = self.get_legal_schema_prompt(practice_area) 
                self.schema_extractors[extractor_key] = SchemaFromTextExtractor(
                    llm=llm,
                    prompt_template=prompt_template
                )
            
            # Extract schema
            extractor = self.schema_extractors[extractor_key]
            schema_result = await extractor.run(text=sample_text)
            
            # Convert to our schema format
            schema = LegalSchema(
                schema_id=f"{practice_area.value}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
                practice_area=practice_area,
                version="1.0",
                node_types=schema_result.to_dict().get("node_types", []),
                relationship_types=schema_result.to_dict().get("relationship_types", []),
                extraction_prompt=self.get_legal_schema_prompt(practice_area),
                validation_rules={},
                created_at=datetime.now(timezone.utc),
                metadata={"sample_text_length": len(sample_text)}
            )
            
            # Cache and store schema
            await self.cache_legal_schema(schema)
            
            logger.info(f"Extracted legal schema for {practice_area.value}: {len(schema.node_types)} node types, {len(schema.relationship_types)} relationship types")
            return schema
            
        except Exception as e:
            logger.error(f"Failed to extract legal schema for {practice_area.value}: {e}")
            return None
    
    async def cache_legal_schema(self, schema: LegalSchema) -> bool:
        """Cache legal schema in storage and memory"""
        try:
            # Store in orchestrator (Supabase)
            schema_data = {
                "node_types": schema.node_types,
                "relationship_types": schema.relationship_types,
                "extraction_prompt": schema.extraction_prompt,
                "validation_rules": schema.validation_rules,
                "metadata": schema.metadata or {}
            }
            
            stored_id = self.orchestrator.store_graphrag_schema(
                practice_area=schema.practice_area.value,
                schema_data=schema_data,
                schema_version=schema.version
            )
            
            if stored_id:
                schema.schema_id = stored_id
                # Cache in memory
                self.schema_cache[schema.practice_area.value] = schema
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to cache legal schema: {e}")
            return False
    
    async def get_cached_schema(self, practice_area: PracticeArea) -> Optional[LegalSchema]:
        """Get cached legal schema for practice area"""
        # Check memory cache first
        if practice_area.value in self.schema_cache:
            return self.schema_cache[practice_area.value]
        
        # Check storage
        try:
            cached_data = self.orchestrator.get_cached_schema(practice_area.value)
            if cached_data:
                schema = LegalSchema(
                    schema_id=cached_data.get("schema_id", ""),
                    practice_area=practice_area,
                    version=cached_data.get("schema_version", "1.0"),
                    node_types=cached_data.get("node_types", []),
                    relationship_types=cached_data.get("relationship_types", []),
                    extraction_prompt=cached_data.get("extraction_prompt", ""),
                    validation_rules=cached_data.get("validation_rules", {}),
                    created_at=datetime.fromisoformat(cached_data.get("created_at", datetime.now(timezone.utc).isoformat())),
                    metadata=cached_data.get("metadata", {})
                )
                
                # Cache in memory
                self.schema_cache[practice_area.value] = schema
                return schema
            
        except Exception as e:
            logger.error(f"Failed to get cached schema for {practice_area.value}: {e}")
        
        return None
    
    # === GraphRAG Pipeline Management ===
    
    async def create_kg_pipeline(self, practice_area: PracticeArea, llm: Any, 
                                neo4j_driver: Any, embedder: Any = None) -> Optional[Any]:
        """
        Create a SimpleKGPipeline for a practice area
        
        Args:
            practice_area: Practice area for specialization
            llm: LLM interface
            neo4j_driver: Neo4j driver
            embedder: Optional embedder for vector processing
            
        Returns:
            Configured SimpleKGPipeline or None
        """
        if not GRAPHRAG_AVAILABLE:
            logger.error("Neo4j GraphRAG not available for pipeline creation")
            return None
        
        try:
            # Get cached schema
            schema = await self.get_cached_schema(practice_area)
            if not schema:
                logger.warning(f"No cached schema found for {practice_area.value}, using default")
            
            # Create pipeline
            pipeline = SimpleKGPipeline(
                llm=llm,
                driver=neo4j_driver,
                embedder=embedder,
                schema=schema.to_dict() if schema else None,
                chunk_size=2000,  # 2k tokens as specified in requirements
                overlap=200,
                from_pdf=True
            )
            
            # Cache pipeline
            self.kg_pipelines[practice_area.value] = pipeline
            
            logger.info(f"Created GraphRAG pipeline for {practice_area.value}")
            return pipeline
            
        except Exception as e:
            logger.error(f"Failed to create GraphRAG pipeline for {practice_area.value}: {e}")
            return None
    
    async def process_legal_document(self, document_text: str, practice_area: PracticeArea,
                                   case_metadata: Dict[str, Any], llm: Any = None,
                                   neo4j_driver: Any = None) -> Optional[GraphRAGResult]:
        """
        Process a legal document through GraphRAG pipeline
        
        Args:
            document_text: Legal document text
            practice_area: Practice area for processing
            case_metadata: Case metadata including IDs and context
            llm: LLM interface
            neo4j_driver: Neo4j driver
            
        Returns:
            GraphRAG processing result or None
        """
        try:
            # Generate global UID for this document
            global_uid = self.orchestrator.generate_global_uid(
                entity_type="legal_document",
                source_system="graphrag",
                source_id=case_metadata.get("cl_opinion_id", ""),
                metadata=case_metadata
            )
            
            # Get or create pipeline
            pipeline_key = practice_area.value
            if pipeline_key not in self.kg_pipelines:
                if not llm or not neo4j_driver:
                    logger.error("LLM and Neo4j driver required for pipeline creation")
                    return None
                    
                pipeline = await self.create_kg_pipeline(practice_area, llm, neo4j_driver)
                if not pipeline:
                    return None
            else:
                pipeline = self.kg_pipelines[pipeline_key]
            
            # Process document
            start_time = datetime.now(timezone.utc)
            
            # Enhanced metadata for GraphRAG processing
            processing_metadata = {
                **case_metadata,
                'global_uid': global_uid.uid,
                'practice_area': practice_area.value,
                'processing_timestamp': start_time.isoformat()
            }
            
            # Run GraphRAG pipeline
            if GRAPHRAG_AVAILABLE:
                pipeline_result = await pipeline.run_async(
                    text=document_text,
                    metadata=processing_metadata
                )
            else:
                # Fallback processing without GraphRAG
                logger.warning("GraphRAG not available, using fallback processing")
                pipeline_result = self._fallback_processing(document_text, processing_metadata)
            
            end_time = datetime.now(timezone.utc)
            processing_time = (end_time - start_time).total_seconds()
            
            # Extract results
            entities = self._extract_entities_from_result(pipeline_result)
            relationships = self._extract_relationships_from_result(pipeline_result)
            embeddings = self._extract_embeddings_from_result(pipeline_result)
            
            # Create result
            result = GraphRAGResult(
                global_uid=global_uid.uid,
                entities=entities,
                relationships=relationships,
                embeddings=embeddings,
                metadata=processing_metadata,
                processing_stats={
                    'processing_time_seconds': processing_time,
                    'entities_extracted': len(entities),
                    'relationships_extracted': len(relationships),
                    'document_length': len(document_text),
                    'practice_area': practice_area.value
                }
            )
            
            logger.info(f"Processed legal document {global_uid.uid}: {len(entities)} entities, {len(relationships)} relationships")
            return result
            
        except Exception as e:
            logger.error(f"Failed to process legal document: {e}")
            return None
    
    def _fallback_processing(self, text: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback processing when GraphRAG is not available"""
        # Simple entity extraction based on patterns
        entities = []
        relationships = []
        
        # Basic legal entity patterns
        import re
        
        # Judge patterns
        judge_pattern = r'Judge\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        judges = re.findall(judge_pattern, text)
        for judge in judges:
            entities.append({
                'type': 'Judge',
                'name': judge,
                'properties': {'extracted_by': 'fallback_pattern'}
            })
        
        # Court patterns  
        court_pattern = r'([A-Z][a-z]+\s+(?:Court|District|Circuit))'
        courts = re.findall(court_pattern, text)
        for court in courts:
            entities.append({
                'type': 'Court',
                'name': court,
                'properties': {'extracted_by': 'fallback_pattern'}
            })
        
        return {
            'nodes': entities,
            'relationships': relationships,
            'embeddings': {}
        }
    
    def _extract_entities_from_result(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract entities from GraphRAG pipeline result"""
        if 'nodes' in result:
            return result['nodes']
        elif 'entities' in result:
            return result['entities']
        else:
            return []
    
    def _extract_relationships_from_result(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract relationships from GraphRAG pipeline result"""
        if 'relationships' in result:
            return result['relationships']
        elif 'edges' in result:
            return result['edges']
        else:
            return []
    
    def _extract_embeddings_from_result(self, result: Dict[str, Any]) -> Dict[str, List[float]]:
        """Extract embeddings from GraphRAG pipeline result"""
        return result.get('embeddings', {})
    
    # === Coordinated Storage Operations ===
    
    async def store_graphrag_results(self, result: GraphRAGResult, jurisdiction: str) -> Dict[str, bool]:
        """
        Store GraphRAG results across all storage backends
        
        Args:
            result: GraphRAG processing result
            jurisdiction: Jurisdiction code
            
        Returns:
            Dictionary of backend success flags
        """
        storage_results = {}
        
        try:
            # Store entities
            for entity in result.entities:
                entity_uid = self.orchestrator.generate_global_uid(
                    entity_type="graphrag_entity",
                    source_system="graphrag",
                    source_id=f"{result.global_uid}_{entity.get('name', 'unnamed')}",
                    metadata={'parent_document': result.global_uid}
                )
                
                entity_result = await self.orchestrator.store_graphrag_entity(
                    global_uid=entity_uid,
                    entity_data=entity,
                    jurisdiction=jurisdiction,
                    practice_area=result.metadata.get('practice_area', 'general')
                )
                
                storage_results[f"entity_{entity_uid.uid}"] = all(entity_result.values())
            
            # Store relationships
            for relationship in result.relationships:
                rel_uid = self.orchestrator.generate_global_uid(
                    entity_type="graphrag_relationship",
                    source_system="graphrag",
                    source_id=f"{result.global_uid}_{relationship.get('type', 'RELATED')}",
                    metadata={'parent_document': result.global_uid}
                )
                
                # Find source and target UIDs (simplified - would need proper mapping)
                source_uid = relationship.get('source', result.global_uid)
                target_uid = relationship.get('target', result.global_uid)
                
                rel_result = await self.orchestrator.store_graphrag_relationship(
                    global_uid=rel_uid,
                    relationship_data=relationship,
                    source_uid=source_uid,
                    target_uid=target_uid
                )
                
                storage_results[f"relationship_{rel_uid.uid}"] = all(rel_result.values())
            
            # Store processing metadata
            metadata_result = await self._store_processing_metadata(result, jurisdiction)
            storage_results["metadata"] = metadata_result
            
            logger.info(f"Stored GraphRAG results for {result.global_uid}: {len(result.entities)} entities, {len(result.relationships)} relationships")
            return storage_results
            
        except Exception as e:
            logger.error(f"Failed to store GraphRAG results: {e}")
            return {"error": False}
    
    async def _store_processing_metadata(self, result: GraphRAGResult, jurisdiction: str) -> bool:
        """Store processing metadata in Supabase"""
        try:
            metadata_record = {
                'global_uid': result.global_uid,
                'pipeline_type': 'graphrag',
                'jurisdiction': jurisdiction,
                'practice_area': result.metadata.get('practice_area', 'general'),
                'entities_count': len(result.entities),
                'relationships_count': len(result.relationships),
                'processing_stats': json.dumps(result.processing_stats),
                'metadata': json.dumps(result.metadata),
                'created_at': datetime.now(timezone.utc).isoformat()
            }
            
            success = self.orchestrator.supabase.insert_record('graphrag_processing_results', metadata_record)
            return bool(success)
            
        except Exception as e:
            logger.error(f"Failed to store processing metadata: {e}")
            return False
    
    # === Validation and Quality Assurance ===
    
    async def validate_extraction_quality(self, result: GraphRAGResult, 
                                         practice_area: PracticeArea) -> Dict[str, Any]:
        """
        Validate GraphRAG extraction quality against practice area expectations
        
        Returns:
            Quality validation report
        """
        validation_report = {
            'global_uid': result.global_uid,
            'practice_area': practice_area.value,
            'validation_timestamp': datetime.now(timezone.utc).isoformat(),
            'overall_score': 0.0,
            'entity_validation': {},
            'relationship_validation': {},
            'issues': []
        }
        
        try:
            config = self.practice_area_configs.get(practice_area, self.practice_area_configs[PracticeArea.GENERAL])
            
            # Entity validation
            extracted_entity_types = set(entity.get('type', 'Unknown') for entity in result.entities)
            expected_entity_types = set(config['primary_entities'])
            
            # Calculate entity coverage
            entity_coverage = len(extracted_entity_types.intersection(expected_entity_types)) / len(expected_entity_types) if expected_entity_types else 0
            validation_report['entity_validation'] = {
                'coverage_score': entity_coverage,
                'extracted_types': list(extracted_entity_types),
                'expected_types': list(expected_entity_types),
                'missing_types': list(expected_entity_types - extracted_entity_types),
                'unexpected_types': list(extracted_entity_types - expected_entity_types)
            }
            
            # Relationship validation
            extracted_rel_types = set(rel.get('type', 'Unknown') for rel in result.relationships)
            expected_rel_types = set(config['primary_relationships'])
            
            # Calculate relationship coverage
            rel_coverage = len(extracted_rel_types.intersection(expected_rel_types)) / len(expected_rel_types) if expected_rel_types else 0
            validation_report['relationship_validation'] = {
                'coverage_score': rel_coverage,
                'extracted_types': list(extracted_rel_types),
                'expected_types': list(expected_rel_types),
                'missing_types': list(expected_rel_types - extracted_rel_types),
                'unexpected_types': list(extracted_rel_types - expected_rel_types)
            }
            
            # Overall quality score
            validation_report['overall_score'] = (entity_coverage + rel_coverage) / 2
            
            # Quality issues
            if entity_coverage < 0.5:
                validation_report['issues'].append("Low entity extraction coverage")
            
            if rel_coverage < 0.3:
                validation_report['issues'].append("Low relationship extraction coverage")
            
            if len(result.entities) == 0:
                validation_report['issues'].append("No entities extracted")
            
            logger.info(f"Validation complete for {result.global_uid}: overall score {validation_report['overall_score']:.2f}")
            return validation_report
            
        except Exception as e:
            logger.error(f"Failed to validate extraction quality: {e}")
            validation_report['issues'].append(f"Validation error: {str(e)}")
            return validation_report
    
    # === Utility Methods ===
    
    def get_practice_area_stats(self) -> Dict[str, Any]:
        """Get statistics for all practice areas"""
        stats = {}
        
        for practice_area in PracticeArea:
            try:
                # Check for cached schema
                has_schema = practice_area.value in self.schema_cache
                
                # Get configuration
                config = self.practice_area_configs.get(practice_area, {})
                
                stats[practice_area.value] = {
                    'has_cached_schema': has_schema,
                    'expected_entity_types': len(config.get('primary_entities', [])),
                    'expected_relationship_types': len(config.get('primary_relationships', [])),
                    'has_pipeline': practice_area.value in self.kg_pipelines
                }
                
            except Exception as e:
                logger.error(f"Failed to get stats for {practice_area.value}: {e}")
                stats[practice_area.value] = {'error': str(e)}
        
        return stats
    
    def close(self):
        """Close GraphRAG Storage Manager"""
        try:
            # Clear caches
            self.schema_cache.clear()
            self.schema_extractors.clear()
            self.kg_pipelines.clear()
            
            # Close orchestrator
            if self.orchestrator:
                self.orchestrator.close()
            
            logger.info("GraphRAG Storage Manager closed")
            
        except Exception as e:
            logger.error(f"Error closing GraphRAG Storage Manager: {e}")