"""
Enhanced Storage Orchestrator for GraphRAG Pipeline
Coordinates complex multi-backend storage operations across GCS, Supabase, Neo4j, and Pinecone.
Ensures data consistency, implements fault-tolerant storage patterns, and maintains data integrity.

Key Features:
- Global UID tracking across all storage backends
- Atomic-like operations using compensation patterns
- Idempotent operations with proper upsert patterns
- ETL checkpoint management and recovery
- Schema storage and caching for GraphRAG
- Data integrity validation across distributed systems
"""

import json
import logging
import uuid
import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Union, Any, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio
from contextlib import asynccontextmanager

# Import existing storage connectors
from .gcs_helper import store_case_document, store_case_json, retrieve_case_document, retrieve_case_json
from .supabase_connector import SupabaseConnector
from .neo4j_connector import Neo4jConnector
from .pinecone_connector import PineconeConnector

# Configure logging
logger = logging.getLogger(__name__)

class StorageOperationType(Enum):
    """Types of storage operations for tracking and rollback"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    UPSERT = "upsert"
    BATCH_CREATE = "batch_create"
    BATCH_UPDATE = "batch_update"

class StorageBackend(Enum):
    """Storage backend types"""
    GCS = "gcs"
    SUPABASE = "supabase"
    NEO4J = "neo4j"
    PINECONE = "pinecone"

@dataclass
class GlobalUID:
    """Global unique identifier for tracking entities across all storage backends"""
    uid: str
    entity_type: str
    source_system: str
    source_id: str
    created_at: datetime
    metadata: Dict[str, Any] = None
    
    @classmethod
    def generate(cls, entity_type: str, source_system: str, source_id: str, 
                 metadata: Dict[str, Any] = None) -> 'GlobalUID':
        """Generate a new global UID"""
        # Create deterministic UUID based on source information
        hash_input = f"{source_system}:{entity_type}:{source_id}"
        uid = str(uuid.uuid5(uuid.NAMESPACE_DNS, hash_input))
        
        return cls(
            uid=uid,
            entity_type=entity_type,
            source_system=source_system,
            source_id=source_id,
            created_at=datetime.now(timezone.utc),
            metadata=metadata or {}
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        return asdict(self)

@dataclass
class StorageOperation:
    """Represents a storage operation for transaction-like behavior"""
    operation_id: str
    operation_type: StorageOperationType
    backend: StorageBackend
    global_uid: str
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    timestamp: datetime
    status: str = "pending"
    error_message: Optional[str] = None
    rollback_data: Optional[Dict[str, Any]] = None
    
    @classmethod
    def create(cls, operation_type: StorageOperationType, backend: StorageBackend,
               global_uid: str, data: Dict[str, Any], metadata: Dict[str, Any] = None) -> 'StorageOperation':
        """Create a new storage operation"""
        return cls(
            operation_id=str(uuid.uuid4()),
            operation_type=operation_type,
            backend=backend,
            global_uid=global_uid,
            data=data,
            metadata=metadata or {},
            timestamp=datetime.now(timezone.utc)
        )

@dataclass 
class ETLCheckpoint:
    """ETL processing checkpoint for resuming interrupted operations"""
    checkpoint_id: str
    pipeline_name: str
    batch_id: str
    global_uids_processed: List[str]
    global_uids_failed: List[str] 
    metadata: Dict[str, Any]
    timestamp: datetime
    status: str = "active"
    
    @classmethod
    def create(cls, pipeline_name: str, batch_id: str, metadata: Dict[str, Any] = None) -> 'ETLCheckpoint':
        """Create a new ETL checkpoint"""
        return cls(
            checkpoint_id=str(uuid.uuid4()),
            pipeline_name=pipeline_name,
            batch_id=batch_id,
            global_uids_processed=[],
            global_uids_failed=[],
            metadata=metadata or {},
            timestamp=datetime.now(timezone.utc)
        )

class EnhancedStorageOrchestrator:
    """
    Enhanced Storage Orchestrator for GraphRAG Pipeline
    
    Coordinates complex multi-backend storage operations across GCS, Supabase, Neo4j, and Pinecone.
    Ensures data consistency, implements fault-tolerant storage patterns, and maintains data integrity.
    """
    
    def __init__(self, 
                 supabase_connector: Optional[SupabaseConnector] = None,
                 neo4j_connector: Optional[Neo4jConnector] = None,
                 pinecone_connector: Optional[PineconeConnector] = None):
        """
        Initialize the Enhanced Storage Orchestrator
        
        Args:
            supabase_connector: Optional pre-initialized Supabase connector
            neo4j_connector: Optional pre-initialized Neo4j connector  
            pinecone_connector: Optional pre-initialized Pinecone connector
        """
        # Initialize storage connectors
        self.supabase = supabase_connector or SupabaseConnector(ensure_tables_exist=True)
        self.neo4j = neo4j_connector or Neo4jConnector()
        self.pinecone = pinecone_connector or PineconeConnector()
        
        # Operation tracking for transaction-like behavior
        self.pending_operations: Dict[str, List[StorageOperation]] = {}
        self.completed_operations: Dict[str, List[StorageOperation]] = {}
        self.failed_operations: Dict[str, List[StorageOperation]] = {}
        
        # Initialize required tables/schemas
        self._initialize_orchestrator_schema()
        
        logger.info("Enhanced Storage Orchestrator initialized")
    
    def _initialize_orchestrator_schema(self):
        """Initialize required tables and schemas for storage orchestration"""
        try:
            # Create global_uid_registry table
            global_uid_table_sql = """
            CREATE TABLE IF NOT EXISTS global_uid_registry (
                uid TEXT PRIMARY KEY,
                entity_type TEXT NOT NULL,
                source_system TEXT NOT NULL,
                source_id TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                metadata JSONB DEFAULT '{}',
                gcs_paths TEXT[],
                supabase_ids TEXT[],
                neo4j_ids TEXT[],
                pinecone_ids TEXT[],
                status TEXT DEFAULT 'active'
            );
            """
            
            # Create etl_checkpoints table  
            checkpoint_table_sql = """
            CREATE TABLE IF NOT EXISTS etl_checkpoints (
                checkpoint_id TEXT PRIMARY KEY,
                pipeline_name TEXT NOT NULL,
                batch_id TEXT NOT NULL,
                global_uids_processed TEXT[],
                global_uids_failed TEXT[],
                metadata JSONB DEFAULT '{}',
                timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                status TEXT DEFAULT 'active'
            );
            """
            
            # Create graphrag_schemas table for schema caching
            schema_table_sql = """
            CREATE TABLE IF NOT EXISTS graphrag_schemas (
                schema_id TEXT PRIMARY KEY,
                practice_area TEXT NOT NULL,
                schema_version TEXT NOT NULL,
                schema_json JSONB NOT NULL,
                node_types JSONB DEFAULT '[]',
                relationship_types JSONB DEFAULT '[]',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                status TEXT DEFAULT 'active',
                usage_count INTEGER DEFAULT 0,
                last_used_at TIMESTAMP WITH TIME ZONE
            );
            """
            
            # Create storage_operations table for audit and rollback
            operations_table_sql = """
            CREATE TABLE IF NOT EXISTS storage_operations (
                operation_id TEXT PRIMARY KEY,
                operation_type TEXT NOT NULL,
                backend TEXT NOT NULL,
                global_uid TEXT NOT NULL,
                data JSONB NOT NULL,
                metadata JSONB DEFAULT '{}',
                timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                status TEXT DEFAULT 'pending',
                error_message TEXT,
                rollback_data JSONB
            );
            """
            
            # Execute table creation
            for sql in [global_uid_table_sql, checkpoint_table_sql, schema_table_sql, operations_table_sql]:
                try:
                    self.supabase.execute_sql(sql)
                    logger.debug(f"Executed schema SQL: {sql[:100]}...")
                except Exception as e:
                    logger.warning(f"Schema initialization SQL failed: {e}")
                    
            logger.info("Storage orchestrator schema initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize orchestrator schema: {e}")
    
    # === Global UID Management ===
    
    def generate_global_uid(self, entity_type: str, source_system: str, source_id: str,
                           metadata: Dict[str, Any] = None) -> GlobalUID:
        """
        Generate and register a new global UID
        
        Args:
            entity_type: Type of entity (case, opinion, entity, relationship, etc.)
            source_system: Source system (courtlistener, graphrag, etc.)
            source_id: Source-specific identifier
            metadata: Additional metadata
            
        Returns:
            GlobalUID object
        """
        global_uid = GlobalUID.generate(entity_type, source_system, source_id, metadata)
        
        # Register in Supabase
        try:
            self.supabase.insert_record('global_uid_registry', {
                'uid': global_uid.uid,
                'entity_type': entity_type,
                'source_system': source_system,
                'source_id': source_id,
                'created_at': global_uid.created_at.isoformat(),
                'metadata': json.dumps(metadata or {}),
                'gcs_paths': [],
                'supabase_ids': [],
                'neo4j_ids': [],
                'pinecone_ids': []
            })
            logger.debug(f"Registered global UID {global_uid.uid}")
        except Exception as e:
            logger.error(f"Failed to register global UID: {e}")
        
        return global_uid
    
    def get_global_uid(self, uid: str) -> Optional[Dict[str, Any]]:
        """Retrieve global UID information"""
        try:
            results = self.supabase.select_records('global_uid_registry', filters={'uid': uid}, limit=1)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Failed to retrieve global UID {uid}: {e}")
            return None
    
    def update_global_uid_references(self, global_uid: str, backend: StorageBackend, reference_id: str):
        """Update backend-specific references for a global UID"""
        try:
            uid_data = self.get_global_uid(global_uid)
            if not uid_data:
                logger.error(f"Global UID {global_uid} not found")
                return False
                
            # Update the appropriate backend reference list
            backend_field = f"{backend.value}_ids"
            current_refs = uid_data.get(backend_field, []) or []
            
            if reference_id not in current_refs:
                current_refs.append(reference_id)
                
                # Update in Supabase
                self.supabase.execute_sql(
                    f"UPDATE global_uid_registry SET {backend_field} = %s, updated_at = NOW() WHERE uid = %s",
                    [current_refs, global_uid]
                )
                
            return True
        except Exception as e:
            logger.error(f"Failed to update global UID references: {e}")
            return False
    
    # === Schema Management ===
    
    def store_graphrag_schema(self, practice_area: str, schema_data: Dict[str, Any], 
                             schema_version: str = None) -> str:
        """
        Store a GraphRAG schema in Supabase for caching and reuse
        
        Args:
            practice_area: Practice area (personal_injury, criminal_defense, etc.)
            schema_data: Schema data dictionary
            schema_version: Optional version string
            
        Returns:
            Schema ID
        """
        schema_id = str(uuid.uuid4())
        version = schema_version or datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        
        try:
            # Extract node and relationship types for indexing
            node_types = []
            relationship_types = []
            
            if 'node_types' in schema_data:
                node_types = [nt.get('label', '') for nt in schema_data['node_types']]
            
            if 'relationship_types' in schema_data:
                relationship_types = [rt.get('label', '') for rt in schema_data['relationship_types']]
            
            # Store schema
            self.supabase.insert_record('graphrag_schemas', {
                'schema_id': schema_id,
                'practice_area': practice_area,
                'schema_version': version,
                'schema_json': json.dumps(schema_data),
                'node_types': json.dumps(node_types),
                'relationship_types': json.dumps(relationship_types),
                'created_at': datetime.now(timezone.utc).isoformat()
            })
            
            logger.info(f"Stored GraphRAG schema {schema_id} for {practice_area}")
            return schema_id
            
        except Exception as e:
            logger.error(f"Failed to store GraphRAG schema: {e}")
            return ""
    
    def get_cached_schema(self, practice_area: str, latest: bool = True) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached GraphRAG schema for a practice area
        
        Args:
            practice_area: Practice area name
            latest: If True, get the most recent version
            
        Returns:
            Schema data dictionary or None
        """
        try:
            filters = {'practice_area': practice_area, 'status': 'active'}
            
            if latest:
                # Get most recent version
                results = self.supabase.execute_sql(
                    "SELECT * FROM graphrag_schemas WHERE practice_area = %s AND status = 'active' ORDER BY created_at DESC LIMIT 1",
                    [practice_area]
                )
            else:
                results = self.supabase.select_records('graphrag_schemas', filters=filters, limit=1)
            
            if results:
                schema_record = results[0] if isinstance(results, list) else results
                
                # Update usage tracking
                self.supabase.execute_sql(
                    "UPDATE graphrag_schemas SET usage_count = usage_count + 1, last_used_at = NOW() WHERE schema_id = %s",
                    [schema_record['schema_id']]
                )
                
                return json.loads(schema_record['schema_json']) if isinstance(schema_record['schema_json'], str) else schema_record['schema_json']
                
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve cached schema for {practice_area}: {e}")
            return None
    
    # === Coordinated Storage Operations ===
    
    async def store_graphrag_entity(self, global_uid: GlobalUID, entity_data: Dict[str, Any],
                                   jurisdiction: str, practice_area: str) -> Dict[str, bool]:
        """
        Store a GraphRAG entity across all relevant backends
        
        Args:
            global_uid: Global unique identifier
            entity_data: Entity data to store
            jurisdiction: Jurisdiction code
            practice_area: Practice area
            
        Returns:
            Dictionary of backend success flags
        """
        transaction_id = str(uuid.uuid4())
        operations = []
        results = {}
        
        try:
            # 1. Store raw entity data in GCS
            gcs_path = store_case_json(
                case_id=global_uid.uid,
                data=entity_data,
                jurisdiction=jurisdiction,
                doc_type=f"graphrag_entity_{entity_data.get('type', 'unknown')}"
            )
            results[StorageBackend.GCS] = bool(gcs_path)
            
            if gcs_path:
                operations.append(StorageOperation.create(
                    StorageOperationType.CREATE,
                    StorageBackend.GCS,
                    global_uid.uid,
                    {'gcs_path': gcs_path}
                ))
                self.update_global_uid_references(global_uid.uid, StorageBackend.GCS, gcs_path)
            
            # 2. Store metadata in Supabase
            supabase_data = {
                'global_uid': global_uid.uid,
                'entity_type': entity_data.get('type', 'unknown'),
                'entity_name': entity_data.get('name', ''),
                'jurisdiction': jurisdiction,
                'practice_area': practice_area,
                'properties': json.dumps(entity_data.get('properties', {})),
                'gcs_path': gcs_path,
                'created_at': datetime.now(timezone.utc).isoformat()
            }
            
            try:
                supabase_result = self.supabase.insert_record('graphrag_entities', supabase_data)
                results[StorageBackend.SUPABASE] = bool(supabase_result)
                
                if supabase_result:
                    operations.append(StorageOperation.create(
                        StorageOperationType.CREATE,
                        StorageBackend.SUPABASE,
                        global_uid.uid,
                        supabase_data
                    ))
                    self.update_global_uid_references(global_uid.uid, StorageBackend.SUPABASE, global_uid.uid)
                    
            except Exception as e:
                logger.error(f"Supabase storage failed: {e}")
                results[StorageBackend.SUPABASE] = False
            
            # 3. Store entity in Neo4j
            neo4j_data = {
                'id': global_uid.uid,
                'name': entity_data.get('name', ''),
                'type': entity_data.get('type', 'Entity'),
                'jurisdiction': jurisdiction,
                'practice_area': practice_area,
                **entity_data.get('properties', {})
            }
            
            neo4j_success = self.neo4j.create_case(neo4j_data)  # Using generic case creation
            results[StorageBackend.NEO4J] = neo4j_success
            
            if neo4j_success:
                operations.append(StorageOperation.create(
                    StorageOperationType.CREATE,
                    StorageBackend.NEO4J,
                    global_uid.uid,
                    neo4j_data
                ))
                self.update_global_uid_references(global_uid.uid, StorageBackend.NEO4J, global_uid.uid)
            
            # 4. Store embeddings in Pinecone if available
            if 'embeddings' in entity_data:
                embeddings = entity_data['embeddings']
                metadata = {
                    'global_uid': global_uid.uid,
                    'entity_type': entity_data.get('type', 'unknown'),
                    'jurisdiction': jurisdiction,
                    'practice_area': practice_area
                }
                
                pinecone_success = self.pinecone.store_embedding(
                    vector=embeddings,
                    id=global_uid.uid,
                    metadata=metadata,
                    jurisdiction=jurisdiction,
                    doc_type='graphrag_entity'
                )
                results[StorageBackend.PINECONE] = pinecone_success
                
                if pinecone_success:
                    operations.append(StorageOperation.create(
                        StorageOperationType.CREATE,
                        StorageBackend.PINECONE,
                        global_uid.uid,
                        {'vector_id': global_uid.uid, 'metadata': metadata}
                    ))
                    self.update_global_uid_references(global_uid.uid, StorageBackend.PINECONE, global_uid.uid)
            else:
                results[StorageBackend.PINECONE] = True  # No embeddings to store
            
            # Record operations
            self.completed_operations[transaction_id] = operations
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to store GraphRAG entity {global_uid.uid}: {e}")
            
            # Attempt rollback if any operations succeeded
            if operations:
                await self._rollback_operations(operations)
            
            return {backend: False for backend in StorageBackend}
    
    async def store_graphrag_relationship(self, global_uid: GlobalUID, relationship_data: Dict[str, Any],
                                         source_uid: str, target_uid: str) -> Dict[str, bool]:
        """
        Store a GraphRAG relationship across relevant backends
        
        Args:
            global_uid: Global unique identifier for the relationship
            relationship_data: Relationship data
            source_uid: Source entity global UID
            target_uid: Target entity global UID
            
        Returns:
            Dictionary of backend success flags
        """
        transaction_id = str(uuid.uuid4())
        operations = []
        results = {}
        
        try:
            # 1. Store relationship metadata in Supabase
            supabase_data = {
                'global_uid': global_uid.uid,
                'relationship_type': relationship_data.get('type', 'RELATED_TO'),
                'source_uid': source_uid,
                'target_uid': target_uid,
                'properties': json.dumps(relationship_data.get('properties', {})),
                'created_at': datetime.now(timezone.utc).isoformat()
            }
            
            try:
                supabase_result = self.supabase.insert_record('graphrag_relationships', supabase_data)
                results[StorageBackend.SUPABASE] = bool(supabase_result)
                
                if supabase_result:
                    operations.append(StorageOperation.create(
                        StorageOperationType.CREATE,
                        StorageBackend.SUPABASE,
                        global_uid.uid,
                        supabase_data
                    ))
                    
            except Exception as e:
                logger.error(f"Supabase relationship storage failed: {e}")
                results[StorageBackend.SUPABASE] = False
            
            # 2. Store relationship in Neo4j
            neo4j_success = self.neo4j.create_citation(
                citing_id=source_uid,
                cited_id=target_uid,
                citation_type=relationship_data.get('type', 'RELATED_TO'),
                metadata=relationship_data.get('properties', {})
            )
            results[StorageBackend.NEO4J] = neo4j_success
            
            if neo4j_success:
                operations.append(StorageOperation.create(
                    StorageOperationType.CREATE,
                    StorageBackend.NEO4J,
                    global_uid.uid,
                    {'source_uid': source_uid, 'target_uid': target_uid, 'type': relationship_data.get('type')}
                ))
            
            # GCS and Pinecone are not typically used for relationships
            results[StorageBackend.GCS] = True
            results[StorageBackend.PINECONE] = True
            
            # Record successful operations
            self.completed_operations[transaction_id] = operations
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to store GraphRAG relationship {global_uid.uid}: {e}")
            
            # Attempt rollback if any operations succeeded
            if operations:
                await self._rollback_operations(operations)
            
            return {backend: False for backend in StorageBackend}
    
    # === ETL Checkpoint Management ===
    
    def create_etl_checkpoint(self, pipeline_name: str, batch_id: str, 
                             metadata: Dict[str, Any] = None) -> ETLCheckpoint:
        """Create a new ETL checkpoint"""
        checkpoint = ETLCheckpoint.create(pipeline_name, batch_id, metadata)
        
        try:
            self.supabase.insert_record('etl_checkpoints', {
                'checkpoint_id': checkpoint.checkpoint_id,
                'pipeline_name': pipeline_name,
                'batch_id': batch_id,
                'global_uids_processed': [],
                'global_uids_failed': [],
                'metadata': json.dumps(metadata or {}),
                'timestamp': checkpoint.timestamp.isoformat(),
                'status': 'active'
            })
            
            logger.info(f"Created ETL checkpoint {checkpoint.checkpoint_id}")
            return checkpoint
            
        except Exception as e:
            logger.error(f"Failed to create ETL checkpoint: {e}")
            return checkpoint
    
    def update_etl_checkpoint(self, checkpoint_id: str, processed_uids: List[str], 
                             failed_uids: List[str] = None):
        """Update ETL checkpoint with processed UIDs"""
        try:
            # Get current checkpoint
            current = self.supabase.select_records('etl_checkpoints', 
                                                 filters={'checkpoint_id': checkpoint_id}, limit=1)
            
            if not current:
                logger.error(f"Checkpoint {checkpoint_id} not found")
                return False
            
            current_data = current[0]
            all_processed = list(set((current_data.get('global_uids_processed') or []) + processed_uids))
            all_failed = list(set((current_data.get('global_uids_failed') or []) + (failed_uids or [])))
            
            # Update checkpoint
            self.supabase.execute_sql(
                """UPDATE etl_checkpoints 
                   SET global_uids_processed = %s, global_uids_failed = %s, 
                       timestamp = NOW() 
                   WHERE checkpoint_id = %s""",
                [all_processed, all_failed, checkpoint_id]
            )
            
            logger.debug(f"Updated checkpoint {checkpoint_id}: {len(all_processed)} processed, {len(all_failed)} failed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update ETL checkpoint: {e}")
            return False
    
    def get_etl_checkpoint(self, checkpoint_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve ETL checkpoint"""
        try:
            results = self.supabase.select_records('etl_checkpoints', 
                                                 filters={'checkpoint_id': checkpoint_id}, limit=1)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Failed to retrieve ETL checkpoint: {e}")
            return None
    
    def resume_from_checkpoint(self, checkpoint_id: str) -> Tuple[List[str], List[str]]:
        """
        Resume processing from an ETL checkpoint
        
        Returns:
            Tuple of (processed_uids, failed_uids)
        """
        checkpoint = self.get_etl_checkpoint(checkpoint_id)
        if not checkpoint:
            return [], []
        
        processed = checkpoint.get('global_uids_processed', []) or []
        failed = checkpoint.get('global_uids_failed', []) or []
        
        logger.info(f"Resuming from checkpoint {checkpoint_id}: {len(processed)} processed, {len(failed)} failed")
        return processed, failed
    
    # === Data Integrity Validation ===
    
    async def validate_global_uid_integrity(self, global_uid: str) -> Dict[str, Any]:
        """
        Validate that a global UID has consistent data across all backends
        
        Returns:
            Validation report with consistency status
        """
        report = {
            'global_uid': global_uid,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'consistency_score': 0.0,
            'backend_status': {},
            'issues': []
        }
        
        try:
            # Get global UID registry entry
            uid_data = self.get_global_uid(global_uid)
            if not uid_data:
                report['issues'].append('Global UID not found in registry')
                return report
            
            backend_checks = 0
            backend_passes = 0
            
            # Check GCS
            gcs_paths = uid_data.get('gcs_paths', []) or []
            if gcs_paths:
                gcs_exists = any(retrieve_case_document(global_uid, 'tx') for path in gcs_paths[:1])  # Check first path
                report['backend_status'][StorageBackend.GCS.value] = gcs_exists
                backend_checks += 1
                if gcs_exists:
                    backend_passes += 1
                else:
                    report['issues'].append('GCS data not found')
            
            # Check Supabase
            supabase_ids = uid_data.get('supabase_ids', []) or []
            if supabase_ids:
                supabase_exists = bool(self.supabase.select_records('graphrag_entities', 
                                                                   filters={'global_uid': global_uid}, limit=1))
                report['backend_status'][StorageBackend.SUPABASE.value] = supabase_exists
                backend_checks += 1
                if supabase_exists:
                    backend_passes += 1
                else:
                    report['issues'].append('Supabase data not found')
            
            # Check Neo4j
            neo4j_ids = uid_data.get('neo4j_ids', []) or []
            if neo4j_ids:
                neo4j_exists = self.neo4j.check_case_exists(global_uid)
                report['backend_status'][StorageBackend.NEO4J.value] = neo4j_exists
                backend_checks += 1
                if neo4j_exists:
                    backend_passes += 1
                else:
                    report['issues'].append('Neo4j data not found')
            
            # Check Pinecone
            pinecone_ids = uid_data.get('pinecone_ids', []) or []
            if pinecone_ids:
                pinecone_data = self.pinecone.fetch_embedding(global_uid, 'tx', 'graphrag_entity')
                pinecone_exists = pinecone_data is not None
                report['backend_status'][StorageBackend.PINECONE.value] = pinecone_exists
                backend_checks += 1
                if pinecone_exists:
                    backend_passes += 1
                else:
                    report['issues'].append('Pinecone data not found')
            
            # Calculate consistency score
            if backend_checks > 0:
                report['consistency_score'] = backend_passes / backend_checks
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to validate global UID integrity: {e}")
            report['issues'].append(f'Validation error: {str(e)}')
            return report
    
    async def validate_batch_integrity(self, global_uids: List[str]) -> Dict[str, Any]:
        """
        Validate integrity for a batch of global UIDs
        
        Returns:
            Batch validation report
        """
        batch_report = {
            'total_uids': len(global_uids),
            'validated': 0,
            'consistency_scores': [],
            'failed_validations': [],
            'overall_score': 0.0,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        for uid in global_uids:
            try:
                validation = await self.validate_global_uid_integrity(uid)
                batch_report['validated'] += 1
                batch_report['consistency_scores'].append(validation['consistency_score'])
                
                if validation['consistency_score'] < 1.0:
                    batch_report['failed_validations'].append({
                        'global_uid': uid,
                        'score': validation['consistency_score'],
                        'issues': validation['issues']
                    })
                    
            except Exception as e:
                logger.error(f"Failed to validate UID {uid}: {e}")
                batch_report['failed_validations'].append({
                    'global_uid': uid,
                    'score': 0.0,
                    'issues': [f'Validation error: {str(e)}']
                })
        
        # Calculate overall score
        if batch_report['consistency_scores']:
            batch_report['overall_score'] = sum(batch_report['consistency_scores']) / len(batch_report['consistency_scores'])
        
        return batch_report
    
    # === Rollback and Recovery ===
    
    async def _rollback_operations(self, operations: List[StorageOperation]):
        """Rollback a list of storage operations using compensation patterns"""
        for operation in reversed(operations):  # Rollback in reverse order
            try:
                if operation.backend == StorageBackend.GCS:
                    # GCS rollback - delete the stored file
                    # Note: GCS helper doesn't have delete method, would need to implement
                    logger.warning(f"GCS rollback not implemented for operation {operation.operation_id}")
                
                elif operation.backend == StorageBackend.SUPABASE:
                    # Supabase rollback - delete the record
                    if operation.operation_type == StorageOperationType.CREATE:
                        self.supabase.delete_record('graphrag_entities', {'global_uid': operation.global_uid})
                        self.supabase.delete_record('graphrag_relationships', {'global_uid': operation.global_uid})
                
                elif operation.backend == StorageBackend.NEO4J:
                    # Neo4j rollback - delete the node/relationship
                    # Would need to implement deletion methods in Neo4j connector
                    logger.warning(f"Neo4j rollback not implemented for operation {operation.operation_id}")
                
                elif operation.backend == StorageBackend.PINECONE:
                    # Pinecone rollback - delete the vector
                    self.pinecone.delete_embedding(operation.global_uid, 'tx', 'graphrag_entity')
                
                logger.info(f"Rolled back operation {operation.operation_id}")
                
            except Exception as e:
                logger.error(f"Failed to rollback operation {operation.operation_id}: {e}")
    
    # === Utility Methods ===
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """Get comprehensive storage statistics across all backends"""
        stats = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'global_uids': {},
            'schemas': {},
            'checkpoints': {},
            'backends': {}
        }
        
        try:
            # Global UID statistics
            uid_counts = self.supabase.execute_sql(
                "SELECT entity_type, COUNT(*) as count FROM global_uid_registry GROUP BY entity_type"
            )
            stats['global_uids'] = {row['entity_type']: row['count'] for row in uid_counts} if uid_counts else {}
            
            # Schema statistics
            schema_counts = self.supabase.execute_sql(
                "SELECT practice_area, COUNT(*) as count FROM graphrag_schemas WHERE status = 'active' GROUP BY practice_area"
            )
            stats['schemas'] = {row['practice_area']: row['count'] for row in schema_counts} if schema_counts else {}
            
            # Checkpoint statistics
            checkpoint_counts = self.supabase.execute_sql(
                "SELECT status, COUNT(*) as count FROM etl_checkpoints GROUP BY status"
            )
            stats['checkpoints'] = {row['status']: row['count'] for row in checkpoint_counts} if checkpoint_counts else {}
            
            # Backend-specific statistics would require additional queries
            stats['backends'] = {
                'gcs': 'available',
                'supabase': 'available',
                'neo4j': 'available',
                'pinecone': 'available'
            }
            
        except Exception as e:
            logger.error(f"Failed to get storage statistics: {e}")
            stats['error'] = str(e)
        
        return stats
    
    def cleanup_old_checkpoints(self, retention_days: int = 30):
        """Clean up old ETL checkpoints"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)
            
            deleted_count = self.supabase.execute_sql(
                "DELETE FROM etl_checkpoints WHERE timestamp < %s AND status != 'active'",
                [cutoff_date.isoformat()]
            )
            
            logger.info(f"Cleaned up old checkpoints: {deleted_count} removed")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old checkpoints: {e}")
    
    def close(self):
        """Close all storage connections"""
        try:
            if hasattr(self.neo4j, 'close'):
                self.neo4j.close()
            logger.info("Enhanced Storage Orchestrator closed")
        except Exception as e:
            logger.error(f"Error closing storage orchestrator: {e}")