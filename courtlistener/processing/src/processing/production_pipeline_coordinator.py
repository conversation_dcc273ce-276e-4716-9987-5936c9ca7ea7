#!/usr/bin/env python3
"""
Production Pipeline Coordinator for Enhanced GraphRAG Workflow

This module orchestrates the complete legal document processing pipeline from
CourtListener API through Enhanced GraphRAG processing to multi-backend storage.

Key Features:
- Complete workflow orchestration from fetch to store
- Intelligent batch processing with cost optimization
- Resume capability with checkpoint management
- Practice area auto-detection and specialization
- Production-grade error handling and recovery
- Comprehensive monitoring and reporting
- Resource management and scaling
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import aiohttp
from contextlib import asynccontextmanager

# Import all required components
from ..api.courtlistener.client import CourtListenerClient
from .enhanced_voyage_graphrag_processor import EnhancedVoyageGraphRAGProcessor
from .storage.enhanced_storage_orchestrator import EnhancedStorageOrchestrator, GlobalUID
from .checkpoint_manager import CheckpointManager
from .cost_monitor import CostMonitor, CostLimits, get_cost_monitor
from .batch_processor import BatchAuditor
from .enhanced_judge_extractor import EnhancedJudgeExtractor
from .legal_domain_optimizations import LegalDomainOptimizer
from .retry_manager import RetryManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProcessingStatus(Enum):
    """Processing status for tracking"""
    PENDING = "pending"
    FETCHING = "fetching"
    PROCESSING = "processing"
    EMBEDDING = "embedding"
    STORING = "storing"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class PracticeArea(Enum):
    """Practice areas for specialization"""
    PERSONAL_INJURY = "personal_injury"
    CRIMINAL_DEFENSE = "criminal_defense"
    FAMILY_LAW = "family_law"
    GENERAL = "general"

@dataclass
class ProcessingMetrics:
    """Metrics for processing performance"""
    total_cases: int = 0
    processed_cases: int = 0
    failed_cases: int = 0
    skipped_cases: int = 0
    total_entities: int = 0
    total_relationships: int = 0
    total_embeddings: int = 0
    total_cost: float = 0.0
    processing_time: float = 0.0
    avg_case_time: float = 0.0
    success_rate: float = 0.0

@dataclass
class PipelineConfig:
    """Configuration for the pipeline coordinator"""
    # CourtListener settings
    courtlistener_api_key: str
    jurisdiction: str = "tex"
    max_cases_per_batch: int = 100
    
    # Processing settings
    max_concurrent_processing: int = 5
    max_workers: int = 10
    chunk_size: int = 2000
    overlap_size: int = 200
    
    # Cost settings
    daily_budget: float = 150.0
    hourly_budget: float = 20.0
    cost_per_case_limit: float = 0.60  # $150/250k = $0.0006 per case
    
    # Storage settings
    enable_gcs: bool = True
    enable_supabase: bool = True
    enable_neo4j: bool = True
    enable_pinecone: bool = True
    
    # Checkpoint settings
    checkpoint_dir: str = "pipeline_checkpoints"
    checkpoint_interval: int = 10  # Checkpoint every N cases
    
    # Monitoring settings
    enable_monitoring: bool = True
    metrics_dir: str = "pipeline_metrics"
    report_interval: int = 60  # Report every N seconds
    
    # Retry settings
    max_retries: int = 3
    retry_delay: float = 1.0
    exponential_backoff: bool = True

class ProductionPipelineCoordinator:
    """
    Production-ready pipeline coordinator for Enhanced GraphRAG workflow
    
    Orchestrates the complete processing pipeline from CourtListener API
    through GraphRAG processing to multi-backend storage with full
    production features including monitoring, checkpointing, and recovery.
    """
    
    def __init__(self, config: PipelineConfig):
        """
        Initialize the production pipeline coordinator
        
        Args:
            config: Pipeline configuration
        """
        self.config = config
        
        # Initialize components
        self.courtlistener = CourtListenerClient(api_key=config.courtlistener_api_key)
        self.voyage_processor = EnhancedVoyageGraphRAGProcessor()
        self.storage_orchestrator = EnhancedStorageOrchestrator()
        self.checkpoint_manager = CheckpointManager(checkpoint_dir=config.checkpoint_dir)
        self.cost_monitor = get_cost_monitor(CostLimits(
            daily_budget=config.daily_budget,
            hourly_budget=config.hourly_budget,
            cost_per_case_limit=config.cost_per_case_limit
        ))
        self.batch_auditor = BatchAuditor(audit_dir=Path(config.metrics_dir) / "audits")
        self.judge_extractor = EnhancedJudgeExtractor()
        self.legal_optimizer = LegalDomainOptimizer()
        self.retry_manager = RetryManager(
            max_retries=config.max_retries,
            retry_delay=config.retry_delay,
            exponential_backoff=config.exponential_backoff
        )
        
        # Processing state
        self.metrics = ProcessingMetrics()
        self.active_cases: Dict[str, ProcessingStatus] = {}
        self.processing_lock = asyncio.Lock()
        self.shutdown_requested = False
        
        # Create necessary directories
        Path(config.checkpoint_dir).mkdir(parents=True, exist_ok=True)
        Path(config.metrics_dir).mkdir(parents=True, exist_ok=True)
        
        logger.info("Production Pipeline Coordinator initialized")
        logger.info(f"Configuration: {asdict(config)}")
    
    async def process_texas_cases(self, 
                                 query: Optional[str] = None,
                                 start_date: Optional[str] = None,
                                 end_date: Optional[str] = None,
                                 max_cases: Optional[int] = None,
                                 resume_checkpoint: Optional[str] = None) -> Dict[str, Any]:
        """
        Main entry point for processing Texas legal cases
        
        Args:
            query: Optional search query for filtering cases
            start_date: Optional start date for case filtering (YYYY-MM-DD)
            end_date: Optional end date for case filtering (YYYY-MM-DD)
            max_cases: Maximum number of cases to process
            resume_checkpoint: Checkpoint ID to resume from
            
        Returns:
            Processing results and metrics
        """
        start_time = time.time()
        batch_id = f"texas_batch_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # Initialize batch
            self.batch_auditor.start_batch({
                "source": "courtlistener",
                "jurisdiction": self.config.jurisdiction,
                "query": query,
                "start_date": start_date,
                "end_date": end_date
            })
            
            # Fetch or resume case list
            if resume_checkpoint:
                cases_to_process = await self._resume_from_checkpoint(resume_checkpoint)
                logger.info(f"Resuming from checkpoint with {len(cases_to_process)} remaining cases")
            else:
                cases_to_process = await self._fetch_texas_cases(query, start_date, end_date, max_cases)
                logger.info(f"Fetched {len(cases_to_process)} Texas cases for processing")
                
                # Create new checkpoint
                checkpoint_id = self.checkpoint_manager.create_checkpoint(
                    process_type="texas_graphrag_pipeline",
                    total_items=len(cases_to_process),
                    metadata={
                        "batch_id": batch_id,
                        "query": query,
                        "jurisdiction": self.config.jurisdiction
                    }
                )
            
            # Process cases in batches
            results = await self._process_case_batch(cases_to_process, batch_id)
            
            # Finalize processing
            processing_time = time.time() - start_time
            self.metrics.processing_time = processing_time
            self.metrics.avg_case_time = processing_time / max(self.metrics.processed_cases, 1)
            self.metrics.success_rate = self.metrics.processed_cases / max(self.metrics.total_cases, 1)
            
            # Complete batch
            self.batch_auditor.complete_batch(
                success_count=self.metrics.processed_cases,
                failure_count=self.metrics.failed_cases
            )
            
            # Generate final report
            final_report = self._generate_final_report(batch_id)
            
            logger.info(f"Pipeline completed: {self.metrics.processed_cases}/{self.metrics.total_cases} cases processed")
            logger.info(f"Total cost: ${self.metrics.total_cost:.4f}, Time: {processing_time:.2f}s")
            
            return final_report
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            self.batch_auditor.complete_batch(
                success_count=self.metrics.processed_cases,
                failure_count=self.metrics.failed_cases
            )
            raise
    
    async def _fetch_texas_cases(self, query: Optional[str], start_date: Optional[str],
                               end_date: Optional[str], max_cases: Optional[int]) -> List[Dict[str, Any]]:
        """Fetch Texas cases from CourtListener API"""
        cases = []
        page = 1
        
        while True:
            # Check cost limits before fetching
            allowed, reason = self.cost_monitor.is_request_allowed()
            if not allowed:
                logger.error(f"Cost limit reached: {reason}")
                break
            
            # Build search parameters
            search_params = {
                "jurisdiction": self.config.jurisdiction,
                "page": page,
                "page_size": min(100, max_cases - len(cases) if max_cases else 100)
            }
            
            if query:
                search_params["q"] = query
            if start_date:
                search_params["filed_after"] = start_date
            if end_date:
                search_params["filed_before"] = end_date
            
            # Fetch cases
            try:
                response = await self.retry_manager.retry_async(
                    self.courtlistener.search_cases,
                    **search_params
                )
                
                results = response.get("results", [])
                cases.extend(results)
                
                # Check if we've reached the limit
                if max_cases and len(cases) >= max_cases:
                    cases = cases[:max_cases]
                    break
                
                # Check if there are more pages
                if not response.get("next"):
                    break
                
                page += 1
                
            except Exception as e:
                logger.error(f"Error fetching cases: {e}")
                break
        
        return cases
    
    async def _resume_from_checkpoint(self, checkpoint_id: str) -> List[Dict[str, Any]]:
        """Resume processing from a checkpoint"""
        checkpoint = self.checkpoint_manager.get_checkpoint(checkpoint_id)
        if not checkpoint:
            raise ValueError(f"Checkpoint {checkpoint_id} not found")
        
        # Get original case list from metadata
        original_cases = checkpoint.metadata.get("cases", [])
        
        # Get remaining cases
        remaining_cases = self.checkpoint_manager.get_remaining_items(
            checkpoint_id,
            [case.get("id", "") for case in original_cases]
        )
        
        # Filter original cases to only include remaining
        remaining_set = set(remaining_cases)
        cases_to_process = [
            case for case in original_cases
            if case.get("id", "") in remaining_set
        ]
        
        return cases_to_process
    
    async def _process_case_batch(self, cases: List[Dict[str, Any]], batch_id: str) -> Dict[str, Any]:
        """Process a batch of cases through the complete pipeline"""
        batch_results = {
            "batch_id": batch_id,
            "total_cases": len(cases),
            "processed": [],
            "failed": [],
            "skipped": []
        }
        
        # Create processing semaphore for concurrency control
        semaphore = asyncio.Semaphore(self.config.max_concurrent_processing)
        
        # Process cases concurrently with controlled parallelism
        tasks = []
        for i, case in enumerate(cases):
            if self.shutdown_requested:
                logger.info("Shutdown requested, stopping processing")
                break
            
            # Create task with semaphore
            task = self._process_single_case_with_semaphore(case, batch_id, semaphore)
            tasks.append(task)
            
            # Checkpoint periodically
            if (i + 1) % self.config.checkpoint_interval == 0:
                await self._save_checkpoint(batch_id, cases, batch_results)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for case, result in zip(cases, results):
            if isinstance(result, Exception):
                logger.error(f"Case {case.get('id')} failed: {result}")
                batch_results["failed"].append({
                    "case_id": case.get("id"),
                    "error": str(result)
                })
                self.metrics.failed_cases += 1
            elif result and result.get("status") == "completed":
                batch_results["processed"].append(result)
                self.metrics.processed_cases += 1
            else:
                batch_results["skipped"].append({
                    "case_id": case.get("id"),
                    "reason": result.get("reason", "Unknown")
                })
                self.metrics.skipped_cases += 1
        
        return batch_results
    
    async def _process_single_case_with_semaphore(self, case: Dict[str, Any], 
                                                 batch_id: str, semaphore: asyncio.Semaphore) -> Dict[str, Any]:
        """Process a single case with semaphore control"""
        async with semaphore:
            return await self._process_single_case(case, batch_id)
    
    async def _process_single_case(self, case: Dict[str, Any], batch_id: str) -> Dict[str, Any]:
        """Process a single case through the complete pipeline"""
        case_id = case.get("id", "unknown")
        start_time = time.time()
        
        try:
            # Update status
            async with self.processing_lock:
                self.active_cases[case_id] = ProcessingStatus.FETCHING
            
            # Step 1: Fetch complete case data including opinions
            logger.info(f"Fetching case {case_id}")
            complete_case = await self._fetch_complete_case(case_id)
            if not complete_case:
                return {"case_id": case_id, "status": "skipped", "reason": "No case data"}
            
            # Step 2: Extract and validate opinion text
            opinion_text = self._extract_opinion_text(complete_case)
            if not opinion_text:
                return {"case_id": case_id, "status": "skipped", "reason": "No opinion text"}
            
            # Step 3: Auto-detect practice area
            practice_area = await self._detect_practice_area(opinion_text, complete_case)
            logger.info(f"Case {case_id} detected as {practice_area}")
            
            # Step 4: Extract metadata and entities
            async with self.processing_lock:
                self.active_cases[case_id] = ProcessingStatus.PROCESSING
            
            case_metadata = await self._extract_case_metadata(complete_case, opinion_text)
            
            # Step 5: Process through Enhanced GraphRAG
            logger.info(f"Processing case {case_id} through GraphRAG")
            graphrag_result = await self.voyage_processor.process_graphrag_document(
                document_text=opinion_text,
                case_metadata=case_metadata,
                practice_area=practice_area
            )
            
            if not graphrag_result:
                return {"case_id": case_id, "status": "failed", "reason": "GraphRAG processing failed"}
            
            # Step 6: Store results across all backends
            async with self.processing_lock:
                self.active_cases[case_id] = ProcessingStatus.STORING
            
            storage_results = await self._store_case_results(
                case_id=case_id,
                graphrag_result=graphrag_result,
                complete_case=complete_case,
                practice_area=practice_area
            )
            
            # Step 7: Record metrics
            processing_time = time.time() - start_time
            case_cost = graphrag_result.get("cost_analysis", {}).get("estimated_cost", 0.0)
            
            self.cost_monitor.record_request(
                tokens_used=graphrag_result.get("cost_analysis", {}).get("tokens_processed", 0),
                cost=case_cost,
                success=True
            )
            
            self.metrics.total_entities += len(graphrag_result.get("graphrag_data", {}).get("entities", []))
            self.metrics.total_relationships += len(graphrag_result.get("graphrag_data", {}).get("relationships", []))
            self.metrics.total_embeddings += graphrag_result.get("processing_stats", {}).get("embeddings_generated", 0)
            self.metrics.total_cost += case_cost
            
            # Update status
            async with self.processing_lock:
                self.active_cases[case_id] = ProcessingStatus.COMPLETED
            
            # Log completion
            self.batch_auditor.complete_document(
                doc_id=case_id,
                success=True,
                metadata={
                    "practice_area": practice_area,
                    "processing_time": processing_time,
                    "cost": case_cost,
                    "entities": self.metrics.total_entities,
                    "relationships": self.metrics.total_relationships
                }
            )
            
            return {
                "case_id": case_id,
                "status": "completed",
                "practice_area": practice_area,
                "processing_time": processing_time,
                "cost": case_cost,
                "storage_results": storage_results,
                "metrics": {
                    "chunks": graphrag_result.get("processing_stats", {}).get("chunks_generated", 0),
                    "entities": len(graphrag_result.get("graphrag_data", {}).get("entities", [])),
                    "relationships": len(graphrag_result.get("graphrag_data", {}).get("relationships", [])),
                    "embeddings": graphrag_result.get("processing_stats", {}).get("embeddings_generated", 0)
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing case {case_id}: {e}")
            
            # Record failure
            self.cost_monitor.record_request(tokens_used=0, cost=0.0, success=False)
            self.batch_auditor.log_error(case_id, str(e))
            self.batch_auditor.complete_document(case_id, success=False)
            
            # Update status
            async with self.processing_lock:
                self.active_cases[case_id] = ProcessingStatus.FAILED
            
            return {
                "case_id": case_id,
                "status": "failed",
                "error": str(e)
            }
    
    async def _fetch_complete_case(self, case_id: str) -> Optional[Dict[str, Any]]:
        """Fetch complete case data from CourtListener"""
        try:
            return await self.retry_manager.retry_async(
                self.courtlistener.get_complete_case,
                cluster_id=case_id,
                full_case=True
            )
        except Exception as e:
            logger.error(f"Failed to fetch case {case_id}: {e}")
            return None
    
    def _extract_opinion_text(self, complete_case: Dict[str, Any]) -> str:
        """Extract opinion text from complete case data"""
        opinions = complete_case.get("opinions", [])
        if not opinions:
            return ""
        
        # Combine all opinion texts
        opinion_texts = []
        for opinion in opinions:
            # Try different fields where opinion text might be stored
            text = (opinion.get("html_with_citations", "") or 
                   opinion.get("html", "") or 
                   opinion.get("plain_text", "") or 
                   opinion.get("text", ""))
            
            if text:
                # Clean HTML if present
                if "<" in text and ">" in text:
                    import re
                    text = re.sub(r'<[^>]+>', '', text)
                
                opinion_texts.append(text.strip())
        
        return "\n\n".join(opinion_texts)
    
    async def _detect_practice_area(self, opinion_text: str, case_data: Dict[str, Any]) -> str:
        """Auto-detect practice area from case content"""
        # Simple keyword-based detection (can be enhanced with ML)
        text_lower = opinion_text.lower()
        
        # Personal injury indicators
        pi_keywords = ["personal injury", "negligence", "damages", "tort", "accident", 
                      "injury", "medical expenses", "pain and suffering", "liability"]
        pi_score = sum(1 for keyword in pi_keywords if keyword in text_lower)
        
        # Criminal defense indicators
        cd_keywords = ["criminal", "defendant", "prosecution", "guilty", "conviction",
                      "sentence", "plea", "crime", "arrest", "miranda"]
        cd_score = sum(1 for keyword in cd_keywords if keyword in text_lower)
        
        # Family law indicators
        fl_keywords = ["divorce", "custody", "child support", "marriage", "alimony",
                      "visitation", "family", "marital", "spouse", "parenting"]
        fl_score = sum(1 for keyword in fl_keywords if keyword in text_lower)
        
        # Determine practice area based on scores
        scores = {
            PracticeArea.PERSONAL_INJURY.value: pi_score,
            PracticeArea.CRIMINAL_DEFENSE.value: cd_score,
            PracticeArea.FAMILY_LAW.value: fl_score
        }
        
        max_area = max(scores, key=scores.get)
        max_score = scores[max_area]
        
        # Require minimum confidence
        if max_score >= 2:
            return max_area
        else:
            return PracticeArea.GENERAL.value
    
    async def _extract_case_metadata(self, complete_case: Dict[str, Any], opinion_text: str) -> Dict[str, Any]:
        """Extract comprehensive metadata from case"""
        cluster = complete_case.get("cluster", {})
        docket = complete_case.get("docket", {})
        court = complete_case.get("court", {})
        
        # Extract judges using enhanced extractor
        judges = await self.judge_extractor.extract_judges_from_text(opinion_text)
        
        metadata = {
            "cl_cluster_id": cluster.get("id", ""),
            "cl_opinion_id": complete_case.get("opinions", [{}])[0].get("id", "") if complete_case.get("opinions") else "",
            "case_name": cluster.get("case_name", ""),
            "case_name_short": cluster.get("case_name_short", ""),
            "docket_number": docket.get("docket_number", ""),
            "date_filed": cluster.get("date_filed", ""),
            "court_id": court.get("id", ""),
            "court_name": court.get("full_name", ""),
            "jurisdiction": self.config.jurisdiction,
            "judges": judges,
            "citation_count": cluster.get("citation_count", 0),
            "precedential_status": cluster.get("precedential_status", ""),
            "source": "courtlistener"
        }
        
        return metadata
    
    async def _store_case_results(self, case_id: str, graphrag_result: Dict[str, Any],
                                complete_case: Dict[str, Any], practice_area: str) -> Dict[str, bool]:
        """Store case results across all enabled backends"""
        storage_results = {}
        global_uid = graphrag_result.get("global_uid", "")
        
        try:
            # Store in GCS if enabled
            if self.config.enable_gcs:
                # Store raw case data
                from .storage.gcs_helper import store_case_json
                gcs_path = store_case_json(
                    case_id=global_uid,
                    data={
                        "complete_case": complete_case,
                        "graphrag_result": graphrag_result,
                        "practice_area": practice_area
                    },
                    jurisdiction=self.config.jurisdiction,
                    doc_type="enhanced_graphrag"
                )
                storage_results["gcs"] = bool(gcs_path)
            
            # Store in Supabase if enabled
            if self.config.enable_supabase:
                # Metadata is already stored by storage orchestrator
                storage_results["supabase"] = True
            
            # Store in Neo4j if enabled
            if self.config.enable_neo4j:
                # Graph data is already stored by storage orchestrator
                storage_results["neo4j"] = True
            
            # Store in Pinecone if enabled
            if self.config.enable_pinecone:
                # Embeddings are already stored by voyage processor
                storage_results["pinecone"] = True
            
            return storage_results
            
        except Exception as e:
            logger.error(f"Error storing case {case_id} results: {e}")
            return {backend: False for backend in ["gcs", "supabase", "neo4j", "pinecone"]}
    
    async def _save_checkpoint(self, batch_id: str, all_cases: List[Dict[str, Any]], 
                             current_results: Dict[str, Any]):
        """Save processing checkpoint"""
        processed_ids = [r.get("case_id") for r in current_results.get("processed", [])]
        failed_ids = [r.get("case_id") for r in current_results.get("failed", [])]
        
        checkpoint_data = {
            "batch_id": batch_id,
            "cases": all_cases,
            "processed_items": processed_ids,
            "failed_items": failed_ids,
            "metrics": asdict(self.metrics),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        checkpoint_id = f"{batch_id}_checkpoint"
        await self.checkpoint_manager.save_checkpoint(checkpoint_id, checkpoint_data)
        logger.info(f"Checkpoint saved: {len(processed_ids)} processed, {len(failed_ids)} failed")
    
    def _generate_final_report(self, batch_id: str) -> Dict[str, Any]:
        """Generate comprehensive final report"""
        return {
            "batch_id": batch_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metrics": asdict(self.metrics),
            "cost_analysis": self.cost_monitor.get_current_metrics(),
            "budget_status": self.cost_monitor.get_budget_status(),
            "processing_summary": {
                "total_cases": self.metrics.total_cases,
                "processed": self.metrics.processed_cases,
                "failed": self.metrics.failed_cases,
                "skipped": self.metrics.skipped_cases,
                "success_rate": f"{self.metrics.success_rate * 100:.2f}%",
                "avg_case_time": f"{self.metrics.avg_case_time:.2f}s",
                "total_time": f"{self.metrics.processing_time:.2f}s"
            },
            "graphrag_summary": {
                "total_entities": self.metrics.total_entities,
                "total_relationships": self.metrics.total_relationships,
                "total_embeddings": self.metrics.total_embeddings,
                "avg_entities_per_case": self.metrics.total_entities / max(self.metrics.processed_cases, 1),
                "avg_relationships_per_case": self.metrics.total_relationships / max(self.metrics.processed_cases, 1)
            },
            "storage_summary": self.storage_orchestrator.get_storage_statistics()
        }
    
    async def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status"""
        async with self.processing_lock:
            active_cases_copy = dict(self.active_cases)
        
        status_counts = {}
        for status in ProcessingStatus:
            status_counts[status.value] = sum(1 for s in active_cases_copy.values() if s == status)
        
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "active_cases": len(active_cases_copy),
            "status_breakdown": status_counts,
            "metrics": asdict(self.metrics),
            "cost_status": self.cost_monitor.get_budget_status(),
            "circuit_breaker": self.cost_monitor.get_current_metrics()["circuit_breaker"]
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all components"""
        health_status = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "overall_status": "healthy",
            "components": {}
        }
        
        # Check CourtListener API
        try:
            courts = self.courtlistener.get_courts(jurisdiction=self.config.jurisdiction)
            health_status["components"]["courtlistener"] = {
                "status": "healthy",
                "courts_available": len(courts)
            }
        except Exception as e:
            health_status["components"]["courtlistener"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["overall_status"] = "unhealthy"
        
        # Check storage backends
        storage_stats = self.storage_orchestrator.get_storage_statistics()
        health_status["components"]["storage"] = {
            "status": "healthy" if not storage_stats.get("error") else "unhealthy",
            "backends": storage_stats.get("backends", {})
        }
        
        # Check cost monitor
        cost_metrics = self.cost_monitor.get_current_metrics()
        health_status["components"]["cost_monitor"] = {
            "status": "healthy" if not cost_metrics["circuit_breaker"]["is_open"] else "degraded",
            "circuit_breaker_open": cost_metrics["circuit_breaker"]["is_open"],
            "daily_budget_remaining": self.cost_monitor.get_budget_status()["daily_budget_remaining"]
        }
        
        return health_status
    
    async def shutdown(self):
        """Graceful shutdown of the pipeline"""
        logger.info("Initiating pipeline shutdown...")
        self.shutdown_requested = True
        
        # Wait for active cases to complete (with timeout)
        timeout = 60  # 60 seconds timeout
        start_time = time.time()
        
        while self.active_cases and (time.time() - start_time) < timeout:
            active_count = len([s for s in self.active_cases.values() if s != ProcessingStatus.COMPLETED])
            if active_count == 0:
                break
            
            logger.info(f"Waiting for {active_count} active cases to complete...")
            await asyncio.sleep(5)
        
        # Save final metrics
        self.cost_monitor.save_metrics(
            str(Path(self.config.metrics_dir) / f"final_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        )
        
        # Close all components
        self.voyage_processor.close()
        self.storage_orchestrator.close()
        
        logger.info("Pipeline shutdown complete")


# Example usage and testing
async def main():
    """Example usage of the Production Pipeline Coordinator"""
    
    # Configure pipeline
    config = PipelineConfig(
        courtlistener_api_key=os.getenv("COURTLISTENER_API_KEY"),
        jurisdiction="tex",
        max_cases_per_batch=10,  # Small batch for testing
        max_concurrent_processing=3,
        daily_budget=150.0,
        checkpoint_dir="pipeline_checkpoints",
        metrics_dir="pipeline_metrics"
    )
    
    # Create coordinator
    coordinator = ProductionPipelineCoordinator(config)
    
    # Perform health check
    health = await coordinator.health_check()
    logger.info(f"Health check: {health}")
    
    # Process Texas personal injury cases
    try:
        results = await coordinator.process_texas_cases(
            query="personal injury negligence",
            start_date="2024-01-01",
            max_cases=5  # Process 5 cases for testing
        )
        
        # Print results
        print("\n=== PROCESSING RESULTS ===")
        print(json.dumps(results, indent=2))
        
    finally:
        # Ensure shutdown
        await coordinator.shutdown()


if __name__ == "__main__":
    asyncio.run(main())