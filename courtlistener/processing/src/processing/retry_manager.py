#!/usr/bin/env python3
"""
Retry Manager for Production Pipeline

Provides intelligent retry logic with exponential backoff, jitter,
and circuit breaker patterns for handling transient failures.
"""

import asyncio
import logging
import random
import time
from typing import Any, Callable, Dict, Optional, TypeVar, Union
from functools import wraps
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

T = TypeVar('T')

@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    max_retries: int = 3
    retry_delay: float = 1.0
    exponential_backoff: bool = True
    backoff_factor: float = 2.0
    max_delay: float = 60.0
    jitter: bool = True
    jitter_factor: float = 0.1
    retry_on_exceptions: tuple = (Exception,)
    exclude_exceptions: tuple = ()

class RetryManager:
    """
    Manages retry logic for the production pipeline
    
    Features:
    - Exponential backoff with configurable factor
    - Jitter to prevent thundering herd
    - Circuit breaker pattern
    - Async and sync support
    - Detailed retry metrics
    """
    
    def __init__(self, 
                 max_retries: int = 3,
                 retry_delay: float = 1.0,
                 exponential_backoff: bool = True,
                 backoff_factor: float = 2.0,
                 max_delay: float = 60.0,
                 jitter: bool = True):
        """
        Initialize retry manager
        
        Args:
            max_retries: Maximum number of retry attempts
            retry_delay: Initial delay between retries in seconds
            exponential_backoff: Whether to use exponential backoff
            backoff_factor: Factor for exponential backoff
            max_delay: Maximum delay between retries
            jitter: Whether to add jitter to delays
        """
        self.config = RetryConfig(
            max_retries=max_retries,
            retry_delay=retry_delay,
            exponential_backoff=exponential_backoff,
            backoff_factor=backoff_factor,
            max_delay=max_delay,
            jitter=jitter
        )
        
        # Retry statistics
        self.stats = {
            'total_attempts': 0,
            'successful_attempts': 0,
            'failed_attempts': 0,
            'retry_counts': {},
            'error_types': {}
        }
        
        # Circuit breaker state
        self.circuit_breaker_state = {
            'is_open': False,
            'failure_count': 0,
            'last_failure_time': None,
            'threshold': 5,  # Open after 5 consecutive failures
            'timeout': timedelta(minutes=5)  # Reset after 5 minutes
        }
    
    def calculate_delay(self, attempt: int) -> float:
        """
        Calculate delay for the given attempt number
        
        Args:
            attempt: Current attempt number (0-based)
            
        Returns:
            Delay in seconds
        """
        if self.config.exponential_backoff:
            delay = min(
                self.config.retry_delay * (self.config.backoff_factor ** attempt),
                self.config.max_delay
            )
        else:
            delay = self.config.retry_delay
        
        # Add jitter if enabled
        if self.config.jitter:
            jitter_amount = delay * self.config.jitter_factor
            delay += random.uniform(-jitter_amount, jitter_amount)
        
        return max(0, delay)  # Ensure non-negative
    
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """
        Determine if we should retry based on the exception and attempt
        
        Args:
            exception: The exception that occurred
            attempt: Current attempt number
            
        Returns:
            True if should retry, False otherwise
        """
        # Check circuit breaker
        if self.is_circuit_open():
            logger.warning("Circuit breaker is open, not retrying")
            return False
        
        # Check max retries
        if attempt >= self.config.max_retries:
            return False
        
        # Check if exception type should be retried
        if self.config.exclude_exceptions:
            if isinstance(exception, self.config.exclude_exceptions):
                return False
        
        if self.config.retry_on_exceptions:
            if not isinstance(exception, self.config.retry_on_exceptions):
                return False
        
        return True
    
    def is_circuit_open(self) -> bool:
        """Check if circuit breaker is open"""
        cb = self.circuit_breaker_state
        
        if not cb['is_open']:
            return False
        
        # Check if timeout has elapsed
        if cb['last_failure_time']:
            elapsed = datetime.now() - cb['last_failure_time']
            if elapsed > cb['timeout']:
                # Reset circuit breaker
                logger.info("Circuit breaker timeout elapsed, resetting")
                cb['is_open'] = False
                cb['failure_count'] = 0
                return False
        
        return True
    
    def record_success(self):
        """Record a successful operation"""
        self.stats['successful_attempts'] += 1
        self.stats['total_attempts'] += 1
        
        # Reset circuit breaker on success
        self.circuit_breaker_state['failure_count'] = 0
    
    def record_failure(self, exception: Exception, attempts: int):
        """Record a failed operation"""
        self.stats['failed_attempts'] += 1
        self.stats['total_attempts'] += 1
        
        # Track retry counts
        retry_count = attempts - 1
        self.stats['retry_counts'][retry_count] = self.stats['retry_counts'].get(retry_count, 0) + 1
        
        # Track error types
        error_type = type(exception).__name__
        self.stats['error_types'][error_type] = self.stats['error_types'].get(error_type, 0) + 1
        
        # Update circuit breaker
        cb = self.circuit_breaker_state
        cb['failure_count'] += 1
        cb['last_failure_time'] = datetime.now()
        
        if cb['failure_count'] >= cb['threshold']:
            cb['is_open'] = True
            logger.error(f"Circuit breaker opened after {cb['failure_count']} failures")
    
    async def retry_async(self, func: Callable[..., T], *args, **kwargs) -> T:
        """
        Retry an async function with configured retry logic
        
        Args:
            func: Async function to retry
            *args: Positional arguments for func
            **kwargs: Keyword arguments for func
            
        Returns:
            Result from successful function call
            
        Raises:
            Last exception if all retries fail
        """
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                # Check circuit breaker before attempting
                if self.is_circuit_open():
                    raise Exception("Circuit breaker is open")
                
                # Attempt the function call
                result = await func(*args, **kwargs)
                
                # Record success
                self.record_success()
                
                # Log if this was a retry that succeeded
                if attempt > 0:
                    logger.info(f"Retry successful after {attempt} attempts for {func.__name__}")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if we should retry
                if not self.should_retry(e, attempt):
                    self.record_failure(e, attempt + 1)
                    raise
                
                # Calculate delay
                delay = self.calculate_delay(attempt)
                
                logger.warning(
                    f"Attempt {attempt + 1}/{self.config.max_retries + 1} failed for {func.__name__}: {e}. "
                    f"Retrying in {delay:.2f}s..."
                )
                
                # Wait before retry
                await asyncio.sleep(delay)
        
        # All retries exhausted
        self.record_failure(last_exception, self.config.max_retries + 1)
        raise last_exception
    
    def retry_sync(self, func: Callable[..., T], *args, **kwargs) -> T:
        """
        Retry a synchronous function with configured retry logic
        
        Args:
            func: Sync function to retry
            *args: Positional arguments for func
            **kwargs: Keyword arguments for func
            
        Returns:
            Result from successful function call
            
        Raises:
            Last exception if all retries fail
        """
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                # Check circuit breaker before attempting
                if self.is_circuit_open():
                    raise Exception("Circuit breaker is open")
                
                # Attempt the function call
                result = func(*args, **kwargs)
                
                # Record success
                self.record_success()
                
                # Log if this was a retry that succeeded
                if attempt > 0:
                    logger.info(f"Retry successful after {attempt} attempts for {func.__name__}")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if we should retry
                if not self.should_retry(e, attempt):
                    self.record_failure(e, attempt + 1)
                    raise
                
                # Calculate delay
                delay = self.calculate_delay(attempt)
                
                logger.warning(
                    f"Attempt {attempt + 1}/{self.config.max_retries + 1} failed for {func.__name__}: {e}. "
                    f"Retrying in {delay:.2f}s..."
                )
                
                # Wait before retry
                time.sleep(delay)
        
        # All retries exhausted
        self.record_failure(last_exception, self.config.max_retries + 1)
        raise last_exception
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get retry statistics"""
        stats = self.stats.copy()
        
        # Calculate success rate
        if stats['total_attempts'] > 0:
            stats['success_rate'] = stats['successful_attempts'] / stats['total_attempts']
        else:
            stats['success_rate'] = 0.0
        
        # Add circuit breaker status
        stats['circuit_breaker'] = {
            'is_open': self.circuit_breaker_state['is_open'],
            'failure_count': self.circuit_breaker_state['failure_count'],
            'threshold': self.circuit_breaker_state['threshold']
        }
        
        return stats
    
    def reset_statistics(self):
        """Reset retry statistics"""
        self.stats = {
            'total_attempts': 0,
            'successful_attempts': 0,
            'failed_attempts': 0,
            'retry_counts': {},
            'error_types': {}
        }
        
        # Reset circuit breaker
        self.circuit_breaker_state['is_open'] = False
        self.circuit_breaker_state['failure_count'] = 0
        self.circuit_breaker_state['last_failure_time'] = None


# Decorator versions for convenience
def retry_async(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator for retrying async functions
    
    Usage:
        @retry_async(max_retries=5, delay=0.5)
        async def my_function():
            ...
    """
    def decorator(func):
        manager = RetryManager(
            max_retries=max_retries,
            retry_delay=delay,
            backoff_factor=backoff
        )
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await manager.retry_async(func, *args, **kwargs)
        
        return wrapper
    
    return decorator


def retry_sync(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator for retrying synchronous functions
    
    Usage:
        @retry_sync(max_retries=5, delay=0.5)
        def my_function():
            ...
    """
    def decorator(func):
        manager = RetryManager(
            max_retries=max_retries,
            retry_delay=delay,
            backoff_factor=backoff
        )
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            return manager.retry_sync(func, *args, **kwargs)
        
        return wrapper
    
    return decorator