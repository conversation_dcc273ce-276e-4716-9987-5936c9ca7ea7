#!/usr/bin/env python3
"""
Enhanced Neo4j GraphRAG Pipeline for Texas Legal Document Processing
Implements v1.9.0 SDK with practice-area specialization and schema evolution
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import hashlib
from collections import defaultdict

import google.generativeai as genai
from neo4j import GraphDatabase
from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
from neo4j_graphrag.llm import VertexAILLM
from neo4j_graphrag.embeddings.base import Embedder
from neo4j_graphrag.experimental.components.text_splitters.langchain import LangChainTextSplitterAdapter
from langchain_text_splitters import RecursiveCharacterTextSplitter
import voyageai
from typing import List

from .cost_monitor import CostMonitor

logger = logging.getLogger(__name__)

class VoyageAIEmbeddings(Embedder):
    """Custom Voyage AI embedder for Neo4j GraphRAG"""
    
    def __init__(
        self, 
        model: str = "voyage-3-large",
        api_key: Optional[str] = None,
        input_type: Optional[str] = None,
        truncation: Optional[bool] = None,
        output_dimension: Optional[int] = 1024  # Default for voyage-3-large
    ):
        self.model = model
        self.input_type = input_type
        self.truncation = truncation
        self.output_dimension = output_dimension
        self.client = voyageai.Client(api_key=api_key)
        
    def embed_query(self, text: str) -> List[float]:
        """Embed query text and return vector embedding."""
        try:
            result = self.client.embed(
                texts=[text],
                model=self.model,
                input_type=self.input_type or "query",
                truncation=self.truncation,
                output_dimension=self.output_dimension
            )
            return result.embeddings[0]
        except Exception as e:
            logger.error(f"Error embedding query with Voyage AI: {e}")
            # Return zero vector as fallback
            return [0.0] * (self.output_dimension or 1024)
        
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed multiple documents."""
        try:
            result = self.client.embed(
                texts=texts,
                model=self.model,
                input_type=self.input_type or "document",
                truncation=self.truncation,
                output_dimension=self.output_dimension
            )
            return result.embeddings
        except Exception as e:
            logger.error(f"Error embedding documents with Voyage AI: {e}")
            # Return zero vectors as fallback
            return [[0.0] * (self.output_dimension or 1024) for _ in texts]

@dataclass
class PracticeAreaConfig:
    """Configuration for practice-area specific pipeline settings"""
    name: str
    entity_types: List[str]
    relationship_types: List[str]
    extraction_prompt_template: str
    schema_enforcement_level: str = "property"  # property, node, or graph
    chunk_size: int = 2000
    chunk_overlap: int = 200
    max_entities_per_chunk: int = 50
    
@dataclass
class SchemaCache:
    """Cache for discovered schemas with versioning"""
    version: str
    timestamp: datetime
    practice_area: str
    entities: Dict[str, Dict[str, Any]]
    relationships: Dict[str, Dict[str, Any]]
    extraction_patterns: Dict[str, str]
    performance_metrics: Dict[str, float] = field(default_factory=dict)

class EnhancedGraphRAGPipeline:
    """
    Enhanced Neo4j GraphRAG Pipeline with practice-area specialization,
    schema discovery, and evolution capabilities
    """
    
    # Practice Area Configurations
    PRACTICE_AREA_CONFIGS = {
        "personal_injury": PracticeAreaConfig(
            name="Personal Injury",
            entity_types=[
                "Case", "Judge", "Court", "Attorney", "Plaintiff", "Defendant",
                "Injury", "Damages", "Settlement", "Verdict", "Expert", "Insurance"
            ],
            relationship_types=[
                "PRESIDED_OVER", "REPRESENTED", "FILED_IN", "AWARDED",
                "TESTIFIED_IN", "CAUSED_BY", "RESULTED_IN", "APPEALED_TO",
                "COVERED_BY", "OPPOSED", "CITED"
            ],
            extraction_prompt_template="""
            Extract entities and relationships from this Texas personal injury case text.
            Focus on: injuries, damages, liability, settlements, insurance coverage.
            Identify medical experts, treating physicians, and causation chains.
            Extract settlement amounts, jury verdicts, and damage calculations.
            """
        ),
        "criminal_defense": PracticeAreaConfig(
            name="Criminal Defense",
            entity_types=[
                "Case", "Judge", "Court", "Prosecutor", "DefenseAttorney", 
                "Defendant", "Charge", "Evidence", "Witness", "Verdict", "Sentence"
            ],
            relationship_types=[
                "PRESIDED_OVER", "PROSECUTED", "DEFENDED", "CHARGED_WITH",
                "TESTIFIED_IN", "SUBMITTED", "CONVICTED_OF", "SENTENCED_TO",
                "APPEALED", "REVERSED", "AFFIRMED"
            ],
            extraction_prompt_template="""
            Extract entities and relationships from this Texas criminal case text.
            Focus on: charges, evidence, constitutional issues, sentencing.
            Identify suppression motions, plea negotiations, and jury instructions.
            Extract sentencing enhancements, probation terms, and appeal grounds.
            """
        ),
        "family_law": PracticeAreaConfig(
            name="Family Law",
            entity_types=[
                "Case", "Judge", "Court", "Attorney", "Petitioner", "Respondent",
                "Child", "Property", "Income", "CustodyArrangement", "Support"
            ],
            relationship_types=[
                "PRESIDED_OVER", "REPRESENTED", "PARENT_OF", "MARRIED_TO",
                "DIVORCED_FROM", "AWARDED_CUSTODY", "ORDERED_SUPPORT",
                "DIVIDED_PROPERTY", "MODIFIED", "ENFORCED"
            ],
            extraction_prompt_template="""
            Extract entities and relationships from this Texas family law case text.
            Focus on: custody, support, property division, protective orders.
            Identify best interest factors, income calculations, and asset valuations.
            Extract parenting plans, support obligations, and enforcement actions.
            """
        )
    }
    
    def __init__(
        self,
        cost_monitor: CostMonitor,
        neo4j_uri: str,
        neo4j_user: str,
        neo4j_password: str,
        gemini_api_key: str,
        voyage_api_key: str,
        practice_area: str = "personal_injury",
        enable_schema_discovery: bool = True,
        cache_dir: Optional[Path] = None
    ):
        self.cost_monitor = cost_monitor
        self.practice_area = practice_area
        self.config = self.PRACTICE_AREA_CONFIGS.get(
            practice_area, 
            self.PRACTICE_AREA_CONFIGS["personal_injury"]
        )
        
        # Initialize Neo4j driver (synchronous for GraphRAG compatibility)
        self.driver = GraphDatabase.driver(
            neo4j_uri,
            auth=(neo4j_user, neo4j_password)
        )
        
        # Initialize Gemini
        genai.configure(api_key=gemini_api_key)
        self.llm = VertexAILLM(
            model_name="gemini-2.0-flash-exp",
            api_key=gemini_api_key,
            temperature=0.1,
            max_tokens=4096
        )
        
        # Initialize Voyage embeddings using custom embedder
        self.embeddings = VoyageAIEmbeddings(
            model="voyage-3-large",
            api_key=voyage_api_key,
            output_dimension=1024  # Use 1024 dimensions for better performance
        )
        
        # Schema management
        self.enable_schema_discovery = enable_schema_discovery
        self.cache_dir = cache_dir or Path("./schema_cache")
        self.cache_dir.mkdir(exist_ok=True)
        self.schema_cache: Optional[SchemaCache] = None
        self._load_schema_cache()
        
        # Pipeline components
        self.text_splitter = None
        self.kg_pipeline = None
        self._initialize_pipeline()
        
        # Metrics tracking
        self.processing_metrics = defaultdict(float)
        
    def _initialize_pipeline(self):
        """Initialize the SimpleKGPipeline with practice-area configuration"""
        # Configure text splitter for legal documents
        recursive_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
            length_function=len,
            separators=[
                "\n\n\n",  # Major section breaks
                "\n\n",    # Paragraph breaks
                "\n",      # Line breaks
                ". ",      # Sentence endings
                "; ",      # Clause breaks
                ", ",      # Comma breaks
                " ",       # Word breaks
                ""         # Character breaks
            ],
            keep_separator=True
        )
        
        # Wrap in Neo4j GraphRAG compatible wrapper
        self.text_splitter = LangChainTextSplitterAdapter(text_splitter=recursive_splitter)
        
        # Initialize KG Pipeline
        self.kg_pipeline = SimpleKGPipeline(
            llm=self.llm,
            embedder=self.embeddings,
            driver=self.driver,
            text_splitter=self.text_splitter,
            perform_entity_resolution=True
        )
    
    def _create_extraction_prompt(self) -> str:
        """Create extraction prompt based on practice area"""
        base_prompt = f"""
        You are a legal knowledge extraction expert specializing in Texas {self.config.name} law.
        
        {self.config.extraction_prompt_template}
        
        For each entity, extract:
        - Unique identifier (construct from available information)
        - Full name or title
        - Relevant dates
        - Key attributes specific to the entity type
        
        For each relationship:
        - Clear connection between entities
        - Date or time period when applicable
        - Strength or certainty of the relationship
        
        Maintain legal precision and cite specific text when extracting information.
        Use Texas legal terminology and standards.
        """
        
        return base_prompt
    
    async def process_documents(
        self,
        documents: List[Dict[str, Any]],
        batch_size: int = 5,
        enable_caching: bool = True
    ) -> Dict[str, Any]:
        """Process documents through the GraphRAG pipeline"""
        logger.info(f"Processing {len(documents)} documents for {self.practice_area}")
        
        results = {
            "processed": 0,
            "failed": 0,
            "entities_extracted": 0,
            "relationships_extracted": 0,
            "costs": {"total": 0.0, "gemini": 0.0, "voyage": 0.0},
            "errors": [],
            "processing_time": 0
        }
        
        start_time = datetime.utcnow()
        
        # Process in batches
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i+batch_size]
            
            try:
                batch_results = await self._process_batch(batch, enable_caching)
                
                # Aggregate results
                results["processed"] += batch_results["processed"]
                results["failed"] += batch_results["failed"]
                results["entities_extracted"] += batch_results["entities_extracted"]
                results["relationships_extracted"] += batch_results["relationships_extracted"]
                
                # Track costs
                for cost_type, amount in batch_results["costs"].items():
                    results["costs"][cost_type] += amount
                
                results["errors"].extend(batch_results.get("errors", []))
                
            except Exception as e:
                logger.error(f"Batch processing error: {e}")
                results["failed"] += len(batch)
                results["errors"].append(str(e))
        
        # Calculate final metrics
        results["processing_time"] = (datetime.utcnow() - start_time).total_seconds()
        results["avg_time_per_doc"] = results["processing_time"] / max(results["processed"], 1)
        
        # Log summary
        logger.info(f"Processed {results['processed']} documents in {results['processing_time']:.2f}s")
        logger.info(f"Extracted {results['entities_extracted']} entities and {results['relationships_extracted']} relationships")
        logger.info(f"Total cost: ${results['costs']['total']:.4f}")
        
        return results
    
    async def _process_batch(
        self,
        documents: List[Dict[str, Any]],
        enable_caching: bool
    ) -> Dict[str, Any]:
        """Process a batch of documents"""
        batch_results = {
            "processed": 0,
            "failed": 0,
            "entities_extracted": 0,
            "relationships_extracted": 0,
            "costs": {"total": 0.0, "gemini": 0.0, "voyage": 0.0},
            "errors": []
        }
        
        for doc in documents:
            try:
                # Process document
                result = await self._process_single_document(doc)
                
                # Update metrics
                batch_results["processed"] += 1
                batch_results["entities_extracted"] += result.get("entities_count", 0)
                batch_results["relationships_extracted"] += result.get("relationships_count", 0)
                
                # Track costs
                if "costs" in result:
                    for cost_type, amount in result["costs"].items():
                        batch_results["costs"][cost_type] += amount
                        batch_results["costs"]["total"] += amount
                    
            except Exception as e:
                logger.error(f"Document processing error: {e}")
                batch_results["failed"] += 1
                batch_results["errors"].append({
                    "document_id": doc.get("id", "unknown"),
                    "error": str(e)
                })
        
        return batch_results
    
    async def _process_single_document(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single document through the pipeline"""
        # Prepare document text
        text = self._prepare_document_text(document)
        
        # Generate unique document ID
        doc_uid = self._generate_document_uid(document)
        
        # Track initial costs
        initial_metrics = self.cost_monitor.get_current_metrics()
        initial_cost = initial_metrics['overall']['total_cost']
        
        # Process through pipeline (metadata is included in text)
        result = await self.kg_pipeline.run_async(text=text)
        
        # Calculate processing cost
        final_metrics = self.cost_monitor.get_current_metrics()
        final_cost = final_metrics['overall']['total_cost']
        processing_cost = final_cost - initial_cost
        
        # Create metadata for tracking
        court_name = document.get("court", "Unknown")
        if isinstance(court_name, dict):
            court_name = court_name.get("name", "Unknown")
        
        metadata = {
            "document_id": doc_uid,
            "practice_area": self.practice_area,
            "court": court_name,
            "date": document.get("date_filed", "Unknown"),
            "case_name": document.get("case_name", "Unknown")
        }
        
        # Process results
        processed_result = {
            "document_id": doc_uid,
            "entities_count": len(result.nodes) if hasattr(result, 'nodes') else 0,
            "relationships_count": len(result.relationships) if hasattr(result, 'relationships') else 0,
            "costs": {
                "gemini": processing_cost * 0.7,  # Estimate gemini portion
                "voyage": processing_cost * 0.3   # Estimate voyage portion
            },
            "metadata": metadata,
            "processing_time": 0  # Would be set by pipeline
        }
        
        return processed_result
    
    def _prepare_document_text(self, document: Dict[str, Any]) -> str:
        """Prepare document text for processing"""
        sections = []
        
        # Add case metadata
        court_name = document.get('court', 'Unknown')
        if isinstance(court_name, dict):
            court_name = court_name.get('name', 'Unknown')
        
        metadata_text = f"""
        Case: {document.get('case_name', 'Unknown')}
        Court: {court_name}
        Date: {document.get('date_filed', 'Unknown')}
        Docket: {document.get('docket_number', 'Unknown')}
        """
        sections.append(metadata_text.strip())
        
        # Add main content
        for content_type in ['plain_text', 'html_lawbox', 'html', 'html_with_citations']:
            if content_type in document and document[content_type]:
                sections.append(document[content_type])
                break
        
        # Add judge information if available
        if 'panel' in document and document['panel']:
            # Handle both string and dict panels
            if isinstance(document['panel'], list) and document['panel']:
                if isinstance(document['panel'][0], str):
                    judge_text = "Judges: " + ", ".join(document['panel'])
                else:
                    judge_text = "Judges: " + ", ".join([
                        judge.get('name', 'Unknown Judge') if isinstance(judge, dict) else str(judge)
                        for judge in document['panel']
                    ])
                sections.append(judge_text)
        
        return "\n\n".join(sections)
    
    def _generate_document_uid(self, document: Dict[str, Any]) -> str:
        """Generate unique identifier for document"""
        # Use existing ID or create from content
        if 'id' in document:
            return f"cl_{document['id']}"
        
        # Fallback to hash of key fields
        unique_string = f"{document.get('case_name', '')}_{document.get('docket_number', '')}_{document.get('date_filed', '')}"
        return hashlib.sha256(unique_string.encode()).hexdigest()[:16]
    
    def _generate_schema_version(self) -> str:
        """Generate schema version identifier"""
        return f"v{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
    
    def _save_schema_cache(self, schema_cache: SchemaCache):
        """Save schema cache to disk"""
        cache_file = self.cache_dir / f"schema_{self.practice_area}_{schema_cache.version}.json"
        try:
            with open(cache_file, 'w') as f:
                json.dump(schema_cache.__dict__, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving schema cache: {e}")
    
    def _load_schema_cache(self):
        """Load most recent schema cache for practice area"""
        try:
            schema_files = list(self.cache_dir.glob(f"schema_{self.practice_area}_*.json"))
            if schema_files:
                # Get most recent
                latest_file = max(schema_files, key=lambda f: f.stat().st_mtime)
                with open(latest_file, 'r') as f:
                    data = json.load(f)
                    # Convert datetime strings back to datetime objects
                    if 'timestamp' in data and isinstance(data['timestamp'], str):
                        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
                    self.schema_cache = SchemaCache(**data)
                    logger.info(f"Loaded schema cache version {self.schema_cache.version}")
        except Exception as e:
            logger.error(f"Error loading schema cache: {e}")
    
    async def get_pipeline_metrics(self) -> Dict[str, Any]:
        """Get comprehensive pipeline metrics"""
        metrics = {
            "practice_area": self.practice_area,
            "schema_version": self.schema_cache.version if self.schema_cache else None,
            "entity_types": len(self.config.entity_types),
            "relationship_types": len(self.config.relationship_types),
            "chunk_size": self.config.chunk_size,
            "processing_metrics": dict(self.processing_metrics),
            "cost_summary": self.cost_monitor.get_summary() if hasattr(self.cost_monitor, 'get_summary') else {},
            "cache_stats": await self._get_cache_stats()
        }
        
        return metrics
    
    async def _get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        cache_files = list(self.cache_dir.glob("*.json"))
        total_size = sum(f.stat().st_size for f in cache_files)
        
        return {
            "cache_files": len(cache_files),
            "total_size_mb": total_size / (1024 * 1024),
            "oldest_cache": min(f.stat().st_mtime for f in cache_files) if cache_files else None,
            "newest_cache": max(f.stat().st_mtime for f in cache_files) if cache_files else None
        }
    
    def close(self):
        """Clean up resources"""
        if self.driver:
            self.driver.close()