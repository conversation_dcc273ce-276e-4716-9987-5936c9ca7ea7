#!/usr/bin/env python3
"""
Enhanced Voyage-context-3 GraphRAG Processor

Integrates Voyage-context-3 embeddings with the Enhanced GraphRAG Pipeline.
Provides cost-optimized batch processing, legal domain optimization,
and hybrid search capabilities across document chunks, entities, and relationships.

Key Features:
- 2k token chunking with optimal overlap for legal documents
- Entity-aware embedding generation for GraphRAG results  
- Practice-area specific embedding namespaces
- Cost-optimized batch processing with caching
- Legal terminology and citation optimization
- Comprehensive testing with real legal data
"""

import asyncio
import json
import logging
import os
import time
import hashlib
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
import aiohttp
from dataclasses import dataclass, asdict
from enum import Enum

# Import existing storage components
from .storage.pinecone_connector import PineconeConnector
from .storage.graphrag_storage_manager import GraphRAGStorageManager, PracticeArea, GraphRAGResult
from .storage.enhanced_storage_orchestrator import EnhancedStorageOrchestrator, GlobalUID

# Configure logging
logger = logging.getLogger(__name__)

class EmbeddingType(Enum):
    """Types of embeddings for processing"""
    DOCUMENT_CHUNKS = "document_chunks"
    ENTITIES = "entities"
    RELATIONSHIPS = "relationships"
    CITATIONS = "citations"

@dataclass
class ChunkingConfig:
    """Configuration for document chunking"""
    chunk_size: int = 2000  # 2k tokens as specified
    overlap_size: int = 200  # 10% overlap
    min_chunk_size: int = 100
    max_chunk_size: int = 2500
    preserve_sentences: bool = True
    legal_boundary_markers: List[str] = None
    
    def __post_init__(self):
        if self.legal_boundary_markers is None:
            self.legal_boundary_markers = [
                "HELD:", "REASONING:", "CONCLUSION:", "DISPOSITION:",
                "I.", "II.", "III.", "IV.", "V.",  # Section markers
                "§", "¶",  # Legal paragraph markers
                "ISSUE:", "FACTS:", "HOLDING:", "ANALYSIS:"
            ]

@dataclass
class EmbeddingStats:
    """Statistics for embedding operations"""
    total_chunks: int = 0
    total_entities: int = 0
    total_relationships: int = 0
    total_embeddings_generated: int = 0
    total_tokens_processed: int = 0
    total_cost: float = 0.0
    processing_time: float = 0.0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class EnhancedVoyageGraphRAGProcessor:
    """
    Enhanced processor for Voyage-context-3 embeddings with GraphRAG integration
    
    Coordinates document chunking, entity extraction, embedding generation,
    and hybrid storage across all backends with cost optimization.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the enhanced processor
        
        Args:
            config: Optional configuration overrides
        """
        self.config = self._initialize_config(config)
        
        # Initialize storage components
        self.storage_orchestrator = EnhancedStorageOrchestrator()
        self.graphrag_manager = GraphRAGStorageManager(self.storage_orchestrator)
        self.pinecone = PineconeConnector()
        
        # Processing statistics
        self.stats = EmbeddingStats()
        
        # Caching for cost optimization
        self.embedding_cache = {}
        self.entity_cache = {}
        
        logger.info("Enhanced Voyage-context-3 GraphRAG Processor initialized")
        logger.info(f"Model: {self.config['model']}, Chunk Size: {self.config['chunking']['chunk_size']}")
    
    def _initialize_config(self, custom_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Initialize processing configuration"""
        default_config = {
            'model': 'voyage-context-3',
            'api_endpoint': 'https://api.voyageai.com/v1/embeddings',
            'dimension': 1024,
            'cost_per_1k_tokens': 0.0001,  # Voyage-context-3 pricing
            'max_retries': 3,
            'retry_delay': 1.0,
            'batch_size': 25,
            'max_concurrent': 5,
            'chunking': {
                'chunk_size': 2000,
                'overlap_size': 200,
                'preserve_sentences': True
            },
            'cost_limits': {
                'max_cost_per_document': 0.10,
                'daily_budget': 100.0,
                'hourly_budget': 20.0
            },
            'optimization': {
                'enable_caching': True,
                'cache_ttl_hours': 24,
                'enable_legal_preprocessing': True,
                'focus_on_entities': True
            }
        }
        
        if custom_config:
            # Deep merge custom config
            self._deep_merge(default_config, custom_config)
        
        return default_config
    
    def _deep_merge(self, base_dict: Dict, update_dict: Dict) -> None:
        """Deep merge configuration dictionaries"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_merge(base_dict[key], value)
            else:
                base_dict[key] = value
    
    # === Document Chunking with Legal Optimization ===
    
    def chunk_legal_document(self, text: str, metadata: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Chunk legal document with 2k token strategy and legal boundary preservation
        
        Args:
            text: Legal document text
            metadata: Optional metadata for chunks
            
        Returns:
            List of chunk dictionaries with text, tokens, and metadata
        """
        if not text.strip():
            return []
        
        config = ChunkingConfig(**self.config['chunking'])
        chunks = []
        
        # Pre-process text for legal optimization
        if self.config['optimization']['enable_legal_preprocessing']:
            text = self._preprocess_legal_text(text)
        
        # Split into sentences for boundary preservation
        sentences = self._split_into_sentences(text)
        
        current_chunk = ""
        current_tokens = 0
        start_token = 0
        chunk_index = 0
        
        for sentence in sentences:
            sentence_tokens = len(sentence.split())
            
            # Check if adding this sentence would exceed chunk size
            if current_tokens + sentence_tokens > config.chunk_size and current_chunk:
                # Finalize current chunk
                chunk = self._create_chunk(
                    text=current_chunk.strip(),
                    chunk_index=chunk_index,
                    start_token=start_token,
                    end_token=start_token + current_tokens,
                    metadata=metadata
                )
                chunks.append(chunk)
                
                # Start new chunk with overlap
                overlap_text = self._get_overlap_text(current_chunk, config.overlap_size)
                overlap_tokens = len(overlap_text.split()) if overlap_text else 0
                
                current_chunk = overlap_text + " " + sentence if overlap_text else sentence
                current_tokens = overlap_tokens + sentence_tokens
                start_token = start_token + current_tokens - overlap_tokens
                chunk_index += 1
            else:
                # Add sentence to current chunk
                current_chunk += " " + sentence if current_chunk else sentence
                current_tokens += sentence_tokens
        
        # Add final chunk
        if current_chunk.strip():
            chunk = self._create_chunk(
                text=current_chunk.strip(),
                chunk_index=chunk_index,
                start_token=start_token,
                end_token=start_token + current_tokens,
                metadata=metadata
            )
            chunks.append(chunk)
        
        logger.info(f"Chunked document into {len(chunks)} chunks (avg {sum(len(c['text'].split()) for c in chunks) / len(chunks):.0f} tokens per chunk)")
        return chunks
    
    def _preprocess_legal_text(self, text: str) -> str:
        """Preprocess legal text for better chunking and embedding"""
        # Normalize legal citations
        import re
        
        # Standardize case citations
        text = re.sub(r'(\d+)\s+([A-Z][a-z\.]*)\s+(\d+)', r'\1 \2 \3', text)
        
        # Normalize statute references
        text = re.sub(r'§\s*(\d+)', r'Section \1', text)
        
        # Clean up excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences with legal boundary awareness"""
        import re
        
        # Legal sentence boundaries
        sentence_endings = r'[.!?](?:\s+|$)'
        sentences = re.split(sentence_endings, text)
        
        # Clean and filter sentences
        clean_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and len(sentence.split()) >= 3:  # Minimum sentence length
                clean_sentences.append(sentence)
        
        return clean_sentences
    
    def _get_overlap_text(self, text: str, overlap_tokens: int) -> str:
        """Get overlap text from end of current chunk"""
        words = text.split()
        if len(words) <= overlap_tokens:
            return text
        
        # Get last N tokens
        overlap_words = words[-overlap_tokens:]
        return " ".join(overlap_words)
    
    def _create_chunk(self, text: str, chunk_index: int, start_token: int, 
                     end_token: int, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create a chunk dictionary with metadata"""
        chunk = {
            'text': text,
            'chunk_index': chunk_index,
            'start_token': start_token,
            'end_token': end_token,
            'token_count': len(text.split()),
            'char_count': len(text),
            'metadata': metadata or {}
        }
        
        # Add legal content indicators
        legal_indicators = self._analyze_legal_content(text)
        chunk['legal_indicators'] = legal_indicators
        
        return chunk
    
    def _analyze_legal_content(self, text: str) -> Dict[str, Any]:
        """Analyze chunk for legal content indicators"""
        import re
        
        indicators = {
            'has_citations': bool(re.search(r'\d+\s+[A-Z][a-z\.]*\s+\d+', text)),
            'has_statute_refs': bool(re.search(r'§\s*\d+|Section\s+\d+', text)),
            'has_court_refs': bool(re.search(r'Court|Judge|Justice', text, re.IGNORECASE)),
            'has_case_names': bool(re.search(r'v\.|vs\.|versus', text, re.IGNORECASE)),
            'has_legal_terms': len(re.findall(r'\b(?:plaintiff|defendant|appellant|appellee|petitioner|respondent)\b', text, re.IGNORECASE)),
            'sentence_count': len(re.split(r'[.!?]', text)),
            'legal_density': 0.0  # Will be calculated
        }
        
        # Calculate legal density
        total_words = len(text.split())
        legal_words = indicators['has_legal_terms']
        indicators['legal_density'] = legal_words / max(total_words, 1)
        
        return indicators
    
    # === Voyage-context-3 Embedding Generation ===
    
    async def generate_embeddings(self, texts: List[str], 
                                embedding_type: EmbeddingType = EmbeddingType.DOCUMENT_CHUNKS) -> List[List[float]]:
        """
        Generate Voyage-context-3 embeddings with cost optimization
        
        Args:
            texts: List of texts to embed
            embedding_type: Type of embedding for optimization
            
        Returns:
            List of embedding vectors
        """
        if not texts:
            return []
        
        # Check cache first if enabled
        if self.config['optimization']['enable_caching']:
            cached_embeddings = self._check_embedding_cache(texts)
            if cached_embeddings:
                logger.info(f"Retrieved {len(cached_embeddings)} embeddings from cache")
                return cached_embeddings
        
        # Estimate cost and validate budget
        estimated_cost = self._estimate_embedding_cost(texts)
        if not self._validate_cost(estimated_cost):
            logger.error(f"Embedding cost ${estimated_cost:.4f} exceeds budget limits")
            return []
        
        # Generate embeddings via API
        embeddings = await self._call_voyage_api(texts, embedding_type)
        
        # Cache results if successful
        if embeddings and self.config['optimization']['enable_caching']:
            self._cache_embeddings(texts, embeddings)
        
        # Update statistics
        self.stats.total_embeddings_generated += len(embeddings) if embeddings else 0
        self.stats.total_tokens_processed += sum(len(text.split()) for text in texts)
        self.stats.total_cost += estimated_cost
        
        return embeddings or []
    
    async def _call_voyage_api(self, texts: List[str], 
                             embedding_type: EmbeddingType) -> Optional[List[List[float]]]:
        """Call Voyage API with retry logic and error handling"""
        url = self.config['api_endpoint']
        headers = {
            "Authorization": f"Bearer {os.getenv('VOYAGE_API_KEY')}",
            "Content-Type": "application/json"
        }
        
        # Optimize input type based on embedding type
        input_type = "document" if embedding_type == EmbeddingType.DOCUMENT_CHUNKS else "query"
        
        payload = {
            "input": texts,
            "model": self.config['model'],
            "input_type": input_type
        }
        
        for attempt in range(self.config['max_retries']):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, headers=headers, json=payload, timeout=120) as response:
                        if response.status == 200:
                            data = await response.json()
                            embeddings = [item['embedding'] for item in data['data']]
                            logger.info(f"Generated {len(embeddings)} Voyage-context-3 embeddings (attempt {attempt + 1})")
                            return embeddings
                        else:
                            error_text = await response.text()
                            logger.error(f"Voyage API error {response.status} (attempt {attempt + 1}): {error_text}")
                            
                            if response.status == 429:  # Rate limit
                                await asyncio.sleep(self.config['retry_delay'] * (2 ** attempt))
                                continue
                            else:
                                break
            
            except Exception as e:
                logger.error(f"Voyage API call failed (attempt {attempt + 1}): {e}")
                if attempt < self.config['max_retries'] - 1:
                    await asyncio.sleep(self.config['retry_delay'] * (2 ** attempt))
        
        return None
    
    def _estimate_embedding_cost(self, texts: List[str]) -> float:
        """Estimate cost for embedding generation"""
        total_tokens = sum(len(text.split()) * 1.3 for text in texts)  # 1.3 factor for tokenization
        cost = (total_tokens / 1000) * self.config['cost_per_1k_tokens']
        return cost
    
    def _validate_cost(self, estimated_cost: float) -> bool:
        """Validate cost against budget limits"""
        max_cost = self.config['cost_limits']['max_cost_per_document']
        
        if estimated_cost > max_cost:
            logger.warning(f"Estimated cost ${estimated_cost:.4f} exceeds max per document ${max_cost:.4f}")
            return False
        
        # Could add daily/hourly budget checks here
        return True
    
    def _check_embedding_cache(self, texts: List[str]) -> Optional[List[List[float]]]:
        """Check cache for existing embeddings"""
        cache_keys = [hashlib.md5(text.encode()).hexdigest() for text in texts]
        cached_embeddings = []
        
        for key in cache_keys:
            if key in self.embedding_cache:
                cached_embeddings.append(self.embedding_cache[key])
            else:
                return None  # All or nothing caching
        
        return cached_embeddings
    
    def _cache_embeddings(self, texts: List[str], embeddings: List[List[float]]) -> None:
        """Cache embeddings for future use"""
        for text, embedding in zip(texts, embeddings):
            cache_key = hashlib.md5(text.encode()).hexdigest()
            self.embedding_cache[cache_key] = embedding
    
    # === GraphRAG Integration ===
    
    async def process_graphrag_document(self, document_text: str, 
                                      case_metadata: Dict[str, Any],
                                      practice_area: str = "general") -> Optional[Dict[str, Any]]:
        """
        Process a legal document through the complete GraphRAG + embeddings pipeline
        
        Args:
            document_text: Legal document text
            case_metadata: Case metadata including IDs and context
            practice_area: Practice area for processing
            
        Returns:
            Complete processing result with embeddings
        """
        start_time = time.time()
        
        try:
            # Convert practice area to enum
            practice_area_enum = PracticeArea(practice_area) if practice_area in [p.value for p in PracticeArea] else PracticeArea.GENERAL
            
            # Generate global UID
            global_uid = self.storage_orchestrator.generate_global_uid(
                entity_type="legal_document",
                source_system="enhanced_graphrag",
                source_id=case_metadata.get("cl_opinion_id", ""),
                metadata=case_metadata
            )
            
            logger.info(f"Processing GraphRAG document {global_uid.uid} for practice area {practice_area}")
            
            # Step 1: Document chunking with 2k token strategy
            chunks = self.chunk_legal_document(document_text, case_metadata)
            if not chunks:
                logger.error("No chunks generated from document")
                return None
            
            self.stats.total_chunks += len(chunks)
            
            # Step 2: Generate chunk embeddings
            chunk_texts = [chunk['text'] for chunk in chunks]
            chunk_embeddings = await self.generate_embeddings(chunk_texts, EmbeddingType.DOCUMENT_CHUNKS)
            
            if not chunk_embeddings or len(chunk_embeddings) != len(chunks):
                logger.error(f"Embedding mismatch: {len(chunk_embeddings)} vs {len(chunks)} chunks")
                return None
            
            # Attach embeddings to chunks
            for chunk, embedding in zip(chunks, chunk_embeddings):
                chunk['embedding'] = embedding
            
            # Step 3: GraphRAG entity and relationship extraction
            graphrag_result = await self.graphrag_manager.process_legal_document(
                document_text=document_text,
                practice_area=practice_area_enum,
                case_metadata=case_metadata
            )
            
            if not graphrag_result:
                logger.warning("GraphRAG extraction failed, proceeding with chunks only")
                entities = []
                relationships = []
            else:
                entities = graphrag_result.entities
                relationships = graphrag_result.relationships
                self.stats.total_entities += len(entities)
                self.stats.total_relationships += len(relationships)
            
            # Step 4: Generate entity embeddings
            entity_embeddings = []
            if entities and self.config['optimization']['focus_on_entities']:
                entity_texts = [self._create_entity_text(entity) for entity in entities]
                entity_embeddings = await self.generate_embeddings(entity_texts, EmbeddingType.ENTITIES)
                
                # Attach embeddings to entities
                for entity, embedding in zip(entities, entity_embeddings or []):
                    entity['embedding'] = embedding
            
            # Step 5: Generate relationship embeddings
            relationship_embeddings = []
            if relationships:
                relationship_texts = [self._create_relationship_text(rel) for rel in relationships]
                relationship_embeddings = await self.generate_embeddings(relationship_texts, EmbeddingType.RELATIONSHIPS)
                
                # Attach embeddings to relationships
                for rel, embedding in zip(relationships, relationship_embeddings or []):
                    rel['embedding'] = embedding
            
            # Step 6: Store in Pinecone with GraphRAG integration
            jurisdiction = case_metadata.get('jurisdiction', 'tx')
            storage_results = await self._store_graphrag_embeddings(
                chunks=chunks,
                entities=entities,
                relationships=relationships,
                global_uid=global_uid.uid,
                jurisdiction=jurisdiction,
                practice_area=practice_area
            )
            
            processing_time = time.time() - start_time
            self.stats.processing_time += processing_time
            
            # Create comprehensive result
            result = {
                'global_uid': global_uid.uid,
                'processing_stats': {
                    'chunks_generated': len(chunks),
                    'entities_extracted': len(entities),
                    'relationships_extracted': len(relationships),
                    'embeddings_generated': len(chunk_embeddings) + len(entity_embeddings) + len(relationship_embeddings),
                    'processing_time_seconds': processing_time,
                    'practice_area': practice_area
                },
                'storage_results': storage_results,
                'cost_analysis': {
                    'estimated_cost': self.stats.total_cost,
                    'tokens_processed': self.stats.total_tokens_processed
                },
                'embeddings': {
                    'document_chunks': chunk_embeddings,
                    'entities': entity_embeddings,
                    'relationships': relationship_embeddings
                },
                'graphrag_data': {
                    'entities': entities,
                    'relationships': relationships
                }
            }
            
            logger.info(f"GraphRAG document processing complete: {len(chunks)} chunks, {len(entities)} entities, {len(relationships)} relationships")
            return result
            
        except Exception as e:
            logger.error(f"GraphRAG document processing failed: {e}")
            self.stats.errors.append(f"Document processing: {str(e)}")
            return None
    
    def _create_entity_text(self, entity: Dict[str, Any]) -> str:
        """Create text representation of entity for embedding"""
        name = entity.get('name', 'Unknown')
        entity_type = entity.get('type', 'Entity')
        properties = entity.get('properties', {})
        
        # Create descriptive text
        text_parts = [f"{entity_type}: {name}"]
        
        # Add relevant properties
        for key, value in properties.items():
            if isinstance(value, str) and value.strip():
                text_parts.append(f"{key}: {value}")
        
        return ". ".join(text_parts)
    
    def _create_relationship_text(self, relationship: Dict[str, Any]) -> str:
        """Create text representation of relationship for embedding"""
        rel_type = relationship.get('type', 'RELATED')
        source = relationship.get('source', 'Unknown')
        target = relationship.get('target', 'Unknown')
        properties = relationship.get('properties', {})
        
        # Create descriptive text
        text = f"{source} {rel_type} {target}"
        
        # Add relevant properties
        if properties:
            prop_text = ", ".join([f"{k}: {v}" for k, v in properties.items() if isinstance(v, str)])
            if prop_text:
                text += f" ({prop_text})"
        
        return text
    
    async def _store_graphrag_embeddings(self, chunks: List[Dict], entities: List[Dict],
                                       relationships: List[Dict], global_uid: str,
                                       jurisdiction: str, practice_area: str) -> Dict[str, bool]:
        """Store all GraphRAG embeddings in Pinecone"""
        storage_results = {}
        
        try:
            # Store document chunks
            if chunks:
                chunk_success = self.pinecone.store_document_chunk_embeddings(
                    chunks=chunks,
                    jurisdiction=jurisdiction,
                    practice_area=practice_area,
                    global_uid=global_uid
                )
                storage_results['chunks'] = chunk_success
            
            # Store entities
            if entities:
                entity_success = self.pinecone.store_entity_embeddings(
                    entities=entities,
                    jurisdiction=jurisdiction,
                    practice_area=practice_area,
                    global_uid=global_uid
                )
                storage_results['entities'] = entity_success
            
            # Store relationships
            if relationships:
                rel_success = self.pinecone.store_relationship_embeddings(
                    relationships=relationships,
                    jurisdiction=jurisdiction,
                    practice_area=practice_area,
                    global_uid=global_uid
                )
                storage_results['relationships'] = rel_success
            
            return storage_results
            
        except Exception as e:
            logger.error(f"Error storing GraphRAG embeddings: {e}")
            return {'error': False}
    
    # === Hybrid Search Implementation ===
    
    async def hybrid_search(self, query: str, jurisdiction: str, practice_area: str,
                          search_types: List[str] = None, top_k: int = 10) -> Dict[str, Any]:
        """
        Perform hybrid search combining vector similarity with GraphRAG knowledge
        
        Args:
            query: Search query
            jurisdiction: Jurisdiction to search in
            practice_area: Practice area to search in
            search_types: Types to search (document_chunks, entities, relationships)
            top_k: Number of results per type
            
        Returns:
            Comprehensive search results
        """
        try:
            # Generate query embedding
            query_embeddings = await self.generate_embeddings([query], EmbeddingType.DOCUMENT_CHUNKS)
            if not query_embeddings:
                logger.error("Failed to generate query embedding")
                return {}
            
            query_vector = query_embeddings[0]
            
            # Perform hybrid search via Pinecone
            search_results = self.pinecone.hybrid_search(
                query_vector=query_vector,
                jurisdiction=jurisdiction,
                practice_area=practice_area,
                search_types=search_types,
                top_k=top_k,
                include_scores=True
            )
            
            # Enhance results with GraphRAG context
            enhanced_results = await self._enhance_search_results(search_results, query)
            
            return {
                'query': query,
                'jurisdiction': jurisdiction,
                'practice_area': practice_area,
                'results': enhanced_results,
                'total_results': sum(len(results) for results in enhanced_results.values()),
                'search_timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Hybrid search failed: {e}")
            return {'error': str(e)}
    
    async def _enhance_search_results(self, search_results: Dict[str, List[Dict]], query: str) -> Dict[str, List[Dict]]:
        """Enhance search results with additional context"""
        enhanced_results = {}
        
        for search_type, results in search_results.items():
            enhanced_results[search_type] = []
            
            for result in results:
                # Add search type context
                result['search_type'] = search_type
                
                # Add relevance indicators based on metadata
                metadata = result.get('metadata', {})
                result['relevance_indicators'] = self._calculate_relevance_indicators(metadata, query)
                
                enhanced_results[search_type].append(result)
        
        return enhanced_results
    
    def _calculate_relevance_indicators(self, metadata: Dict[str, Any], query: str) -> Dict[str, Any]:
        """Calculate relevance indicators for search results"""
        indicators = {
            'has_legal_entities': False,
            'entity_match': False,
            'query_terms_found': 0,
            'legal_density': metadata.get('legal_density', 0.0)
        }
        
        # Check for legal entities
        entity_fields = ['entity_name', 'entity_type', 'relationship_type']
        for field in entity_fields:
            if field in metadata:
                indicators['has_legal_entities'] = True
                break
        
        # Check for query term matches in metadata
        query_terms = query.lower().split()
        for term in query_terms:
            for key, value in metadata.items():
                if isinstance(value, str) and term in value.lower():
                    indicators['query_terms_found'] += 1
                    break
        
        return indicators
    
    # === Statistics and Monitoring ===
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics"""
        return {
            'embedding_stats': asdict(self.stats),
            'cache_stats': {
                'cached_embeddings': len(self.embedding_cache),
                'cache_enabled': self.config['optimization']['enable_caching']
            },
            'cost_analysis': {
                'total_cost': self.stats.total_cost,
                'avg_cost_per_chunk': self.stats.total_cost / max(self.stats.total_chunks, 1),
                'tokens_per_dollar': self.stats.total_tokens_processed / max(self.stats.total_cost, 0.001)
            },
            'performance_metrics': {
                'total_processing_time': self.stats.processing_time,
                'avg_time_per_document': self.stats.processing_time / max(len(self.stats.errors) + 1, 1),
                'embeddings_per_second': self.stats.total_embeddings_generated / max(self.stats.processing_time, 0.1)
            }
        }
    
    def close(self):
        """Close processor and cleanup resources"""
        try:
            if hasattr(self, 'pinecone'):
                self.pinecone.close()
            
            if hasattr(self, 'graphrag_manager'):
                self.graphrag_manager.close()
            
            if hasattr(self, 'storage_orchestrator'):
                self.storage_orchestrator.close()
            
            logger.info("Enhanced Voyage-context-3 GraphRAG Processor closed")
            
        except Exception as e:
            logger.error(f"Error closing processor: {e}")