#!/usr/bin/env python3
"""
Legal Domain Optimizations for GraphRAG Processing

Provides specialized optimizations for legal document processing including
citation normalization, legal entity recognition, and domain-specific chunking.
"""

import re
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class LegalEntityType(Enum):
    """Types of legal entities"""
    CASE = "case"
    STATUTE = "statute"
    REGULATION = "regulation"
    COURT = "court"
    JUDGE = "judge"
    ATTORNEY = "attorney"
    PARTY = "party"
    LEGAL_CONCEPT = "legal_concept"

@dataclass
class LegalCitation:
    """Represents a legal citation"""
    text: str
    volume: Optional[str] = None
    reporter: Optional[str] = None
    page: Optional[str] = None
    year: Optional[str] = None
    court: Optional[str] = None
    citation_type: str = "case"
    normalized_form: Optional[str] = None

class LegalDomainOptimizer:
    """
    Optimizes document processing for the legal domain
    
    Features:
    - Legal citation detection and normalization
    - Legal entity recognition
    - Domain-specific text preprocessing
    - Legal terminology expansion
    - Practice area classification hints
    """
    
    def __init__(self):
        """Initialize the legal domain optimizer"""
        # Citation patterns
        self.citation_patterns = {
            'federal': re.compile(r'(\d+)\s+(U\.S\.)\s+(\d+)(?:\s+\((\d{4})\))?'),
            'federal_reporter': re.compile(r'(\d+)\s+(F\.\d?d|F\.\s?Supp\.?\s?\d?d?)\s+(\d+)'),
            'state': re.compile(r'(\d+)\s+([A-Z][a-z]+\.?\s?\d?d?)\s+(\d+)'),
            'statute': re.compile(r'(\d+)\s+U\.S\.C\.\s+§\s*(\d+)'),
            'texas_statute': re.compile(r'Tex\.\s+([A-Z][a-z]+\.?)\s+Code\s+(?:Ann\.\s+)?§\s*(\d+(?:\.\d+)?)'),
        }
        
        # Legal terms and concepts
        self.legal_terms = {
            'personal_injury': [
                'negligence', 'damages', 'tort', 'liability', 'injury', 
                'accident', 'compensation', 'medical expenses', 'pain and suffering',
                'duty of care', 'breach', 'causation', 'proximate cause'
            ],
            'criminal_defense': [
                'defendant', 'prosecution', 'guilty', 'conviction', 'sentence',
                'plea', 'criminal', 'charge', 'arrest', 'miranda', 'evidence',
                'reasonable doubt', 'jury', 'verdict', 'indictment'
            ],
            'family_law': [
                'divorce', 'custody', 'child support', 'alimony', 'marriage',
                'visitation', 'marital property', 'spouse', 'parenting plan',
                'best interests of the child', 'community property'
            ]
        }
        
        # Court abbreviations
        self.court_abbreviations = {
            'S. Ct.': 'Supreme Court',
            'Sup. Ct.': 'Supreme Court',
            'App.': 'Appeals',
            'Cir.': 'Circuit',
            'Dist.': 'District',
            'Tex.': 'Texas',
            'Cal.': 'California',
            'N.Y.': 'New York'
        }
        
        # Legal document sections
        self.section_markers = [
            'BACKGROUND', 'FACTS', 'PROCEDURAL HISTORY', 'STANDARD OF REVIEW',
            'DISCUSSION', 'ANALYSIS', 'CONCLUSION', 'HOLDING', 'ORDER',
            'OPINION', 'DISSENT', 'CONCURRENCE', 'JUDGMENT'
        ]
    
    def optimize_legal_text(self, text: str) -> Dict[str, Any]:
        """
        Apply comprehensive legal domain optimizations to text
        
        Args:
            text: Legal document text
            
        Returns:
            Dictionary with optimized text and metadata
        """
        # Normalize citations
        normalized_text, citations = self.normalize_citations(text)
        
        # Extract legal entities
        entities = self.extract_legal_entities(normalized_text)
        
        # Identify document sections
        sections = self.identify_sections(normalized_text)
        
        # Enhance with legal terminology
        enhanced_text = self.enhance_legal_terminology(normalized_text)
        
        # Calculate legal complexity score
        complexity_score = self.calculate_legal_complexity(text)
        
        return {
            'optimized_text': enhanced_text,
            'original_text': text,
            'citations': citations,
            'entities': entities,
            'sections': sections,
            'complexity_score': complexity_score,
            'optimization_metadata': {
                'citations_found': len(citations),
                'entities_found': len(entities),
                'sections_identified': len(sections)
            }
        }
    
    def normalize_citations(self, text: str) -> Tuple[str, List[LegalCitation]]:
        """
        Normalize legal citations in text
        
        Args:
            text: Text containing legal citations
            
        Returns:
            Tuple of (normalized text, list of citations found)
        """
        citations = []
        normalized_text = text
        
        # Process each citation pattern
        for citation_type, pattern in self.citation_patterns.items():
            matches = pattern.finditer(text)
            
            for match in matches:
                groups = match.groups()
                
                if citation_type == 'federal':
                    citation = LegalCitation(
                        text=match.group(0),
                        volume=groups[0],
                        reporter=groups[1],
                        page=groups[2],
                        year=groups[3] if len(groups) > 3 else None,
                        citation_type='case'
                    )
                elif citation_type == 'statute':
                    citation = LegalCitation(
                        text=match.group(0),
                        volume=groups[0],
                        page=groups[1],
                        citation_type='statute'
                    )
                else:
                    citation = LegalCitation(
                        text=match.group(0),
                        citation_type=citation_type
                    )
                
                # Create normalized form
                citation.normalized_form = self._create_normalized_citation(citation)
                citations.append(citation)
                
                # Replace in text with normalized form
                normalized_text = normalized_text.replace(
                    citation.text,
                    f"[CITATION: {citation.normalized_form}]"
                )
        
        return normalized_text, citations
    
    def _create_normalized_citation(self, citation: LegalCitation) -> str:
        """Create normalized citation format"""
        if citation.citation_type == 'case' and citation.volume and citation.reporter and citation.page:
            norm = f"{citation.volume} {citation.reporter} {citation.page}"
            if citation.year:
                norm += f" ({citation.year})"
            return norm
        elif citation.citation_type == 'statute':
            return citation.text.replace(' ', '_').upper()
        else:
            return citation.text
    
    def extract_legal_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract legal entities from text
        
        Args:
            text: Legal document text
            
        Returns:
            List of extracted entities
        """
        entities = []
        
        # Extract case names (v. or vs.)
        case_pattern = re.compile(r'([A-Z][a-zA-Z\s&,\.]+?)\s+v\.?\s+([A-Z][a-zA-Z\s&,\.]+?)(?=\s*[,\.]|\s+\d)')
        for match in case_pattern.finditer(text):
            entities.append({
                'text': match.group(0),
                'type': LegalEntityType.CASE.value,
                'plaintiff': match.group(1).strip(),
                'defendant': match.group(2).strip()
            })
        
        # Extract court names
        court_pattern = re.compile(r'(Supreme Court|Court of Appeals|District Court|Circuit Court)[^,\.]{0,50}')
        for match in court_pattern.finditer(text):
            entities.append({
                'text': match.group(0),
                'type': LegalEntityType.COURT.value,
                'name': match.group(0).strip()
            })
        
        # Extract judge names (simplified pattern)
        judge_pattern = re.compile(r'(Judge|Justice|Magistrate)\s+([A-Z][a-z]+(?:\s+[A-Z]\.?\s*)?[A-Z][a-z]+)')
        for match in judge_pattern.finditer(text):
            entities.append({
                'text': match.group(0),
                'type': LegalEntityType.JUDGE.value,
                'title': match.group(1),
                'name': match.group(2)
            })
        
        # Extract legal concepts
        for concept_type, terms in self.legal_terms.items():
            for term in terms:
                if re.search(r'\b' + re.escape(term) + r'\b', text, re.IGNORECASE):
                    entities.append({
                        'text': term,
                        'type': LegalEntityType.LEGAL_CONCEPT.value,
                        'category': concept_type
                    })
        
        return entities
    
    def identify_sections(self, text: str) -> List[Dict[str, Any]]:
        """
        Identify document sections in legal text
        
        Args:
            text: Legal document text
            
        Returns:
            List of identified sections with positions
        """
        sections = []
        
        for marker in self.section_markers:
            # Look for section headers
            pattern = re.compile(rf'^\s*{marker}\s*$', re.MULTILINE | re.IGNORECASE)
            
            for match in pattern.finditer(text):
                sections.append({
                    'type': marker,
                    'start': match.start(),
                    'end': match.end(),
                    'text': match.group(0).strip()
                })
        
        # Sort by position
        sections.sort(key=lambda x: x['start'])
        
        # Add section content
        for i, section in enumerate(sections):
            if i < len(sections) - 1:
                section['content_end'] = sections[i + 1]['start']
            else:
                section['content_end'] = len(text)
            
            section['content'] = text[section['end']:section['content_end']].strip()
        
        return sections
    
    def enhance_legal_terminology(self, text: str) -> str:
        """
        Enhance text with legal terminology markers
        
        Args:
            text: Legal document text
            
        Returns:
            Enhanced text with terminology markers
        """
        enhanced_text = text
        
        # Add terminology markers for better embedding
        terminology_mappings = {
            r'\bnegligence\b': '[LEGAL_CONCEPT: negligence]',
            r'\bliability\b': '[LEGAL_CONCEPT: liability]',
            r'\bstatute of limitations\b': '[LEGAL_CONCEPT: statute_of_limitations]',
            r'\bproximate cause\b': '[LEGAL_CONCEPT: proximate_cause]',
            r'\bres judicata\b': '[LEGAL_CONCEPT: res_judicata]',
            r'\bstare decisis\b': '[LEGAL_CONCEPT: stare_decisis]',
            r'\bdue process\b': '[LEGAL_CONCEPT: due_process]',
            r'\bburden of proof\b': '[LEGAL_CONCEPT: burden_of_proof]'
        }
        
        for pattern, replacement in terminology_mappings.items():
            enhanced_text = re.sub(pattern, replacement, enhanced_text, flags=re.IGNORECASE)
        
        return enhanced_text
    
    def calculate_legal_complexity(self, text: str) -> float:
        """
        Calculate legal complexity score for text
        
        Args:
            text: Legal document text
            
        Returns:
            Complexity score between 0 and 1
        """
        complexity_factors = {
            'citation_density': 0.0,
            'legal_term_density': 0.0,
            'sentence_complexity': 0.0,
            'section_count': 0.0
        }
        
        word_count = len(text.split())
        if word_count == 0:
            return 0.0
        
        # Citation density
        citation_count = len(re.findall(r'\d+\s+[A-Z][a-z]+\.?\s*\d*[a-z]?\s+\d+', text))
        complexity_factors['citation_density'] = min(citation_count / (word_count / 100), 1.0)
        
        # Legal term density
        legal_term_count = 0
        for terms in self.legal_terms.values():
            for term in terms:
                legal_term_count += len(re.findall(r'\b' + re.escape(term) + r'\b', text, re.IGNORECASE))
        complexity_factors['legal_term_density'] = min(legal_term_count / (word_count / 100), 1.0)
        
        # Sentence complexity (average words per sentence)
        sentences = re.split(r'[.!?]+', text)
        avg_sentence_length = sum(len(s.split()) for s in sentences) / max(len(sentences), 1)
        complexity_factors['sentence_complexity'] = min(avg_sentence_length / 40, 1.0)  # 40 words = max complexity
        
        # Section count
        section_count = len(self.identify_sections(text))
        complexity_factors['section_count'] = min(section_count / 10, 1.0)  # 10 sections = max complexity
        
        # Calculate weighted average
        weights = {
            'citation_density': 0.3,
            'legal_term_density': 0.3,
            'sentence_complexity': 0.2,
            'section_count': 0.2
        }
        
        complexity_score = sum(
            complexity_factors[factor] * weight 
            for factor, weight in weights.items()
        )
        
        return round(complexity_score, 3)
    
    def suggest_chunk_boundaries(self, text: str, target_chunk_size: int = 2000) -> List[int]:
        """
        Suggest optimal chunk boundaries for legal text
        
        Args:
            text: Legal document text
            target_chunk_size: Target size for chunks in characters
            
        Returns:
            List of suggested boundary positions
        """
        boundaries = [0]
        sections = self.identify_sections(text)
        
        # If we have clear sections, use them as natural boundaries
        if sections:
            for section in sections:
                if section['start'] - boundaries[-1] >= target_chunk_size / 2:
                    boundaries.append(section['start'])
        
        # Otherwise, look for paragraph boundaries
        else:
            paragraphs = re.finditer(r'\n\s*\n', text)
            current_pos = 0
            
            for para in paragraphs:
                para_pos = para.start()
                
                if para_pos - current_pos >= target_chunk_size:
                    boundaries.append(para_pos)
                    current_pos = para_pos
        
        # Ensure we don't have chunks that are too small
        filtered_boundaries = [boundaries[0]]
        for b in boundaries[1:]:
            if b - filtered_boundaries[-1] >= target_chunk_size / 2:
                filtered_boundaries.append(b)
        
        # Add end boundary
        if len(text) - filtered_boundaries[-1] > target_chunk_size / 4:
            filtered_boundaries.append(len(text))
        
        return filtered_boundaries