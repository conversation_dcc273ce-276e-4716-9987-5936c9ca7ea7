{"test_id": "simple_graphrag_verification", "timestamp": "2025-08-01T12:42:20.780123", "processing_time": 8.74880313873291, "total_cost": 0.0, "judges_found": 3, "judges": [{"name": "JONES", "confidence": 0.85, "method": "text_plain_text"}, {"name": "SMITH", "confidence": 0.85, "method": "text_plain_text"}, {"name": "ELROD", "confidence": 0.85, "method": "text_plain_text"}], "metrics": {"api": {"method": "CourtListener API", "judges_found": 0, "confidence_avg": 0.0, "token_cost": 0.0, "processing_time": 0.021416902542114258}, "pattern": {"method": "Enhanced <PERSON><PERSON>", "judges_found": 3, "confidence_avg": 0.85, "token_cost": 0.0, "processing_time": 0.0065288543701171875}, "graphrag": {"method": "GraphRAG", "judges_found": 0, "confidence_avg": 0.0, "token_cost": 0.0, "processing_time": 8.720702171325684}, "validation": {"method": "Validation", "judges_found": 3, "confidence_avg": 0.85, "token_cost": 0.0, "processing_time": 2.86102294921875e-06}}, "graphrag_success": false, "pattern_success": true}