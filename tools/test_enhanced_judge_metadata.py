#!/usr/bin/env python3
"""
Test Enhanced Judge <PERSON><PERSON><PERSON>
Verify the new JSONB structure supports API-sourced data
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add processing to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'courtlistener', 'processing', 'src'))

from processing.judge_extraction_service import JudgeExtractionService, JudgeInfo

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_enhanced_metadata_schema():
    """Test the enhanced judge metadata schema with different data types"""
    logger.info("📊 TESTING ENHANCED JUDGE METADATA SCHEMA")
    logger.info("=" * 80)
    
    judge_service = JudgeExtractionService()
    
    # Test Case 1: API-sourced judge data
    api_judge = JudgeInfo(
        name="<PERSON>",
        full_name="<PERSON>",
        confidence=0.95,
        source="courtlistener",
        court="ca5",
        role="author",
        extraction_method="api_people",
        metadata={
            'person_id': '12345',
            'date_created': '2020-01-01T00:00:00Z',
            'date_modified': '2023-01-01T00:00:00Z',
            'court_info': {
                'court_id': 'ca5',
                'court_name': 'U.S. Court of Appeals for the Fifth Circuit',
                'jurisdiction': 'federal'
            },
            'positions_count': 3,
            'api_source': 'people_endpoint'
        }
    )
    
    # Test Case 2: Text-extracted judge data
    text_judge = JudgeInfo(
        name="Jane Doe",
        full_name="Jane M. Doe",
        confidence=0.75,
        source="courtlistener",
        court="txsd",
        role="panel",
        extraction_method="text_pattern",
        metadata={
            'pattern': r'Before ([A-Z][A-Z\s]+), Circuit Judge',
            'source_field': 'plain_text'
        }
    )
    
    # Test Case 3: Mixed confidence judge
    mixed_judge = JudgeInfo(
        name="Robert Johnson",
        full_name="Robert L. Johnson",
        confidence=0.45,
        source="caselaw_access_project",
        court="unknown",
        role="author",
        extraction_method="metadata_author"
    )
    
    judges = [api_judge, text_judge, mixed_judge]
    
    # Create test case data
    case_data = {
        'id': 'test_case_metadata',
        'case_name': 'Test Case for Metadata Schema',
        'court_id': 'ca5',
        'source': 'courtlistener'
    }
    
    # Generate enhanced metadata
    logger.info("🔧 Creating enhanced judge metadata...")
    metadata = judge_service.create_judge_metadata(judges, case_data)
    
    if not metadata:
        logger.error("❌ Failed to create metadata")
        return False
    
    # Verify metadata structure
    logger.info("✅ Enhanced metadata created successfully!")
    logger.info(f"📊 Metadata Structure Analysis:")
    
    # Basic stats
    stats = metadata['extraction_stats']
    logger.info(f"   Total Extracted: {stats['total_extracted']}")
    logger.info(f"   Average Confidence: {stats['avg_confidence']:.2f}")
    logger.info(f"   Sources: {stats['sources']}")
    logger.info(f"   Methods: {stats['methods']}")
    
    # Enhanced stats
    logger.info(f"   API Extracted: {stats['api_extracted']}")
    logger.info(f"   Text Extracted: {stats['text_extracted']}")
    logger.info(f"   API Success Rate: {stats['api_success_rate']:.2f}")
    logger.info(f"   High Confidence Count: {stats['high_confidence_count']}")
    logger.info(f"   Medium Confidence Count: {stats['medium_confidence_count']}")
    logger.info(f"   Low Confidence Count: {stats['low_confidence_count']}")
    
    # Data quality indicators
    logger.info(f"   Has API Data: {stats['has_api_data']}")
    logger.info(f"   Has Biographical Info: {stats['has_biographical_info']}")
    logger.info(f"   Has Court Assignments: {stats['has_court_assignments']}")
    
    # Primary judge
    logger.info(f"   Primary Judge: {metadata['primary_judge']}")
    
    # Individual judge analysis
    logger.info(f"\n👨‍⚖️ Individual Judge Analysis:")
    for i, judge_data in enumerate(metadata['judges'], 1):
        logger.info(f"   Judge {i}: {judge_data['name']}")
        logger.info(f"      Full Name: {judge_data['full_name']}")
        logger.info(f"      Confidence: {judge_data['confidence']:.2f}")
        logger.info(f"      Method: {judge_data['extraction_method']}")
        logger.info(f"      Role: {judge_data['role']}")
        logger.info(f"      Court: {judge_data['court']}")
        
        # API-specific data
        if judge_data.get('api_data'):
            api_data = judge_data['api_data']
            logger.info(f"      API Person ID: {api_data['person_id']}")
            logger.info(f"      API Source: {api_data['api_source']}")
            logger.info(f"      Date Created: {api_data['date_created']}")
        
        # Court assignments
        if judge_data.get('court_assignments'):
            assignments = judge_data['court_assignments']
            logger.info(f"      Court Assignments: {len(assignments)}")
            for assignment in assignments:
                logger.info(f"         - {assignment['court_name']} ({assignment['jurisdiction']})")
        
        # Positions history
        if judge_data.get('positions_history'):
            history = judge_data['positions_history']
            logger.info(f"      Positions History: {history['total_positions']} positions")
            logger.info(f"      History Source: {history['source']}")
        
        logger.info("")
    
    # Validation tests
    logger.info("🧪 Schema Validation Tests:")
    
    # Test 1: Required fields present
    required_fields = ['judges', 'primary_judge', 'extraction_stats']
    missing_fields = [field for field in required_fields if field not in metadata]
    if missing_fields:
        logger.error(f"❌ Missing required fields: {missing_fields}")
        return False
    else:
        logger.info("✅ All required fields present")
    
    # Test 2: Judge data structure
    for i, judge_data in enumerate(metadata['judges']):
        required_judge_fields = ['name', 'confidence', 'source', 'extraction_method']
        missing_judge_fields = [field for field in required_judge_fields if field not in judge_data]
        if missing_judge_fields:
            logger.error(f"❌ Judge {i+1} missing fields: {missing_judge_fields}")
            return False
    logger.info("✅ All judge records have required fields")
    
    # Test 3: API data structure (when present)
    api_judges = [j for j in metadata['judges'] if j.get('api_data')]
    if api_judges:
        for judge_data in api_judges:
            api_data = judge_data['api_data']
            if 'person_id' not in api_data:
                logger.error(f"❌ API judge missing person_id: {judge_data['name']}")
                return False
        logger.info(f"✅ All {len(api_judges)} API judges have proper structure")
    
    # Test 4: Statistics consistency
    expected_total = len(judges)
    actual_total = stats['total_extracted']
    if expected_total != actual_total:
        logger.error(f"❌ Statistics inconsistency: expected {expected_total}, got {actual_total}")
        return False
    logger.info("✅ Statistics are consistent")
    
    # Test 5: Confidence calculations
    expected_avg = sum(j.confidence for j in judges) / len(judges)
    actual_avg = stats['avg_confidence']
    if abs(expected_avg - actual_avg) > 0.01:
        logger.error(f"❌ Confidence calculation error: expected {expected_avg:.2f}, got {actual_avg:.2f}")
        return False
    logger.info("✅ Confidence calculations are correct")
    
    logger.info("🎉 ALL SCHEMA VALIDATION TESTS PASSED!")
    return True

def main():
    """Main test execution"""
    logger.info("🎯 ENHANCED JUDGE METADATA SCHEMA TESTING")
    logger.info("=" * 100)
    
    try:
        success = test_enhanced_metadata_schema()
        
        if success:
            logger.info("🎉 SUCCESS: Enhanced judge metadata schema is working perfectly!")
            logger.info("✅ Schema supports both API and text-extracted data")
            logger.info("✅ Backward compatibility maintained")
            logger.info("✅ Enhanced statistics and quality indicators working")
            return True
        else:
            logger.error("❌ FAILURE: Enhanced metadata schema has issues")
            return False
            
    except Exception as e:
        logger.error(f"💥 Testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
