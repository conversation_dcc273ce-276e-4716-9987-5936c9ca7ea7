#!/usr/bin/env python3
"""
Test Production Pipeline Coordinator

Comprehensive test and demonstration of the Production Pipeline Coordinator
that orchestrates the complete Enhanced GraphRAG workflow with real CourtListener data.

This script demonstrates:
1. Pipeline initialization and configuration
2. Fetching Texas legal cases from CourtListener
3. Processing through Enhanced GraphRAG with Voyage-context-3
4. Multi-backend storage (GCS, Supabase, Neo4j, Pinecone)
5. Cost monitoring and budget management
6. Checkpoint and resume functionality
7. Production monitoring and reporting
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional

# Add the processing module to path
sys.path.append(str(Path(__file__).parent / "courtlistener" / "processing" / "src"))

from processing.production_pipeline_coordinator import (
    ProductionPipelineCoordinator,
    PipelineConfig,
    ProcessingStatus,
    PracticeArea
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('production_pipeline_test.log')
    ]
)
logger = logging.getLogger(__name__)

class PipelineDemo:
    """Demonstration of the Production Pipeline Coordinator"""
    
    def __init__(self):
        """Initialize the demo"""
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Verify required environment variables
        required_vars = [
            "COURTLISTENER_API_KEY",
            "VOYAGE_API_KEY",
            "NEO4J_URI",
            "NEO4J_USERNAME",
            "NEO4J_PASSWORD",
            "SUPABASE_URL",
            "SUPABASE_KEY",
            "PINECONE_API_KEY",
            "PINECONE_ENVIRONMENT",
            "GOOGLE_APPLICATION_CREDENTIALS"
        ]
        
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            logger.error(f"Missing required environment variables: {missing_vars}")
            raise ValueError("Missing required environment variables")
        
        # Create directories
        Path("pipeline_checkpoints").mkdir(exist_ok=True)
        Path("pipeline_metrics").mkdir(exist_ok=True)
        Path("pipeline_reports").mkdir(exist_ok=True)
    
    async def demo_basic_processing(self):
        """Demonstrate basic case processing"""
        logger.info("=== DEMO: Basic Case Processing ===")
        
        # Configure pipeline with conservative settings for demo
        config = PipelineConfig(
            courtlistener_api_key=os.getenv("COURTLISTENER_API_KEY"),
            jurisdiction="tex",
            max_cases_per_batch=5,  # Small batch for demo
            max_concurrent_processing=2,
            max_workers=4,
            chunk_size=2000,
            overlap_size=200,
            daily_budget=10.0,  # Limited budget for demo
            hourly_budget=2.0,
            cost_per_case_limit=0.50,
            checkpoint_interval=2,  # Checkpoint every 2 cases
            enable_monitoring=True
        )
        
        # Create coordinator
        coordinator = ProductionPipelineCoordinator(config)
        
        try:
            # Process recent Texas personal injury cases
            logger.info("Processing recent Texas personal injury cases...")
            
            results = await coordinator.process_texas_cases(
                query="personal injury AND negligence",
                start_date=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"),
                max_cases=3  # Process only 3 cases for demo
            )
            
            # Save results
            report_path = Path("pipeline_reports") / f"basic_demo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_path, 'w') as f:
                json.dump(results, f, indent=2)
            
            logger.info(f"Results saved to {report_path}")
            
            # Print summary
            self._print_processing_summary(results)
            
        finally:
            await coordinator.shutdown()
    
    async def demo_checkpoint_resume(self):
        """Demonstrate checkpoint and resume functionality"""
        logger.info("=== DEMO: Checkpoint and Resume ===")
        
        config = PipelineConfig(
            courtlistener_api_key=os.getenv("COURTLISTENER_API_KEY"),
            jurisdiction="tex",
            max_cases_per_batch=10,
            max_concurrent_processing=2,
            checkpoint_interval=2,
            daily_budget=10.0
        )
        
        coordinator = ProductionPipelineCoordinator(config)
        
        try:
            # Start processing with intentional interruption
            logger.info("Starting processing with planned interruption...")
            
            # Create a task that we'll interrupt
            process_task = asyncio.create_task(
                coordinator.process_texas_cases(
                    query="criminal defense",
                    max_cases=6
                )
            )
            
            # Wait a bit then simulate interruption
            await asyncio.sleep(15)  # Let it process a few cases
            logger.info("Simulating interruption...")
            process_task.cancel()
            
            try:
                await process_task
            except asyncio.CancelledError:
                logger.info("Processing interrupted as planned")
            
            # Get the latest checkpoint
            checkpoints = coordinator.checkpoint_manager.get_resumable_checkpoints()
            if checkpoints:
                latest_checkpoint = checkpoints[0]
                logger.info(f"Found checkpoint: {latest_checkpoint.checkpoint_id}")
                
                # Show checkpoint progress
                progress = coordinator.checkpoint_manager.get_progress_summary(
                    latest_checkpoint.checkpoint_id
                )
                logger.info(f"Checkpoint progress: {json.dumps(progress, indent=2)}")
                
                # Resume from checkpoint
                logger.info("Resuming from checkpoint...")
                
                # Create new coordinator (simulating restart)
                new_coordinator = ProductionPipelineCoordinator(config)
                
                results = await new_coordinator.process_texas_cases(
                    resume_checkpoint=latest_checkpoint.checkpoint_id
                )
                
                # Save results
                report_path = Path("pipeline_reports") / f"resume_demo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(report_path, 'w') as f:
                    json.dump(results, f, indent=2)
                
                self._print_processing_summary(results)
                
                await new_coordinator.shutdown()
            
        finally:
            await coordinator.shutdown()
    
    async def demo_practice_area_detection(self):
        """Demonstrate practice area auto-detection"""
        logger.info("=== DEMO: Practice Area Auto-Detection ===")
        
        config = PipelineConfig(
            courtlistener_api_key=os.getenv("COURTLISTENER_API_KEY"),
            jurisdiction="tex",
            max_concurrent_processing=1,  # Process sequentially for clear demo
            daily_budget=10.0
        )
        
        coordinator = ProductionPipelineCoordinator(config)
        
        try:
            # Test different practice areas
            test_queries = [
                ("personal injury negligence damages", "Personal Injury"),
                ("criminal defendant prosecution guilty", "Criminal Defense"),
                ("divorce custody child support", "Family Law")
            ]
            
            all_results = {}
            
            for query, expected_area in test_queries:
                logger.info(f"\nTesting query: '{query}' (expecting {expected_area})")
                
                results = await coordinator.process_texas_cases(
                    query=query,
                    max_cases=1  # Just one case per query
                )
                
                # Extract detected practice area
                if results.get("processing_summary", {}).get("processed", 0) > 0:
                    # Find the processed case in batch results
                    metrics = results.get("metrics", {})
                    logger.info(f"Processed case - Expected: {expected_area}")
                    
                all_results[expected_area] = results
            
            # Save combined results
            report_path = Path("pipeline_reports") / f"practice_area_demo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_path, 'w') as f:
                json.dump(all_results, f, indent=2)
            
        finally:
            await coordinator.shutdown()
    
    async def demo_monitoring_and_health(self):
        """Demonstrate monitoring and health check capabilities"""
        logger.info("=== DEMO: Monitoring and Health Checks ===")
        
        config = PipelineConfig(
            courtlistener_api_key=os.getenv("COURTLISTENER_API_KEY"),
            jurisdiction="tex",
            enable_monitoring=True,
            report_interval=10  # Report every 10 seconds
        )
        
        coordinator = ProductionPipelineCoordinator(config)
        
        try:
            # Perform initial health check
            logger.info("Performing health check...")
            health = await coordinator.health_check()
            logger.info(f"Health status:\n{json.dumps(health, indent=2)}")
            
            # Start processing with monitoring
            monitor_task = asyncio.create_task(self._monitor_processing(coordinator))
            
            # Process cases
            process_task = asyncio.create_task(
                coordinator.process_texas_cases(
                    query="contract breach",
                    max_cases=5
                )
            )
            
            # Wait for processing to complete
            results = await process_task
            monitor_task.cancel()
            
            # Final health check
            final_health = await coordinator.health_check()
            logger.info(f"Final health status:\n{json.dumps(final_health, indent=2)}")
            
            # Get cost analysis
            cost_metrics = coordinator.cost_monitor.get_current_metrics()
            logger.info(f"Cost analysis:\n{json.dumps(cost_metrics, indent=2)}")
            
        finally:
            await coordinator.shutdown()
    
    async def _monitor_processing(self, coordinator: ProductionPipelineCoordinator):
        """Monitor processing progress"""
        try:
            while True:
                status = await coordinator.get_processing_status()
                
                logger.info(f"\n--- Processing Status ---")
                logger.info(f"Active cases: {status['active_cases']}")
                logger.info(f"Status breakdown: {status['status_breakdown']}")
                logger.info(f"Processed: {status['metrics']['processed_cases']}")
                logger.info(f"Cost so far: ${status['metrics']['total_cost']:.4f}")
                logger.info(f"Circuit breaker: {'OPEN' if status['circuit_breaker']['is_open'] else 'CLOSED'}")
                
                await asyncio.sleep(10)
                
        except asyncio.CancelledError:
            logger.info("Monitoring stopped")
    
    def _print_processing_summary(self, results: Dict[str, Any]):
        """Print a formatted processing summary"""
        print("\n" + "="*50)
        print("PROCESSING SUMMARY")
        print("="*50)
        
        summary = results.get("processing_summary", {})
        print(f"Total Cases: {summary.get('total_cases', 0)}")
        print(f"Processed: {summary.get('processed', 0)}")
        print(f"Failed: {summary.get('failed', 0)}")
        print(f"Skipped: {summary.get('skipped', 0)}")
        print(f"Success Rate: {summary.get('success_rate', '0%')}")
        print(f"Total Time: {summary.get('total_time', '0s')}")
        print(f"Avg Case Time: {summary.get('avg_case_time', '0s')}")
        
        graphrag = results.get("graphrag_summary", {})
        print(f"\nGraphRAG Results:")
        print(f"  Total Entities: {graphrag.get('total_entities', 0)}")
        print(f"  Total Relationships: {graphrag.get('total_relationships', 0)}")
        print(f"  Total Embeddings: {graphrag.get('total_embeddings', 0)}")
        print(f"  Avg Entities/Case: {graphrag.get('avg_entities_per_case', 0):.1f}")
        print(f"  Avg Relationships/Case: {graphrag.get('avg_relationships_per_case', 0):.1f}")
        
        cost = results.get("cost_analysis", {}).get("overall", {})
        print(f"\nCost Analysis:")
        print(f"  Total Cost: ${cost.get('total_cost', 0):.4f}")
        print(f"  Avg Cost/Request: ${cost.get('avg_cost_per_request', 0):.4f}")
        print(f"  Total Tokens: {cost.get('total_tokens', 0):,}")
        
        print("="*50 + "\n")


async def main():
    """Main demonstration function"""
    demo = PipelineDemo()
    
    # Menu for different demos
    demos = {
        "1": ("Basic Processing", demo.demo_basic_processing),
        "2": ("Checkpoint & Resume", demo.demo_checkpoint_resume),
        "3": ("Practice Area Detection", demo.demo_practice_area_detection),
        "4": ("Monitoring & Health", demo.demo_monitoring_and_health),
        "5": ("Run All Demos", None)
    }
    
    print("\nProduction Pipeline Coordinator Demo")
    print("=" * 40)
    for key, (name, _) in demos.items():
        print(f"{key}. {name}")
    print("0. Exit")
    
    choice = input("\nSelect demo (0-5): ").strip()
    
    if choice == "0":
        return
    elif choice == "5":
        # Run all demos
        for key, (name, func) in demos.items():
            if key != "5" and func:
                print(f"\n{'='*60}")
                print(f"Running: {name}")
                print('='*60)
                await func()
                print("\nPress Enter to continue to next demo...")
                input()
    elif choice in demos and demos[choice][1]:
        await demos[choice][1]()
    else:
        print("Invalid choice")


if __name__ == "__main__":
    # Run the demo
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        logger.error(f"Demo failed: {e}", exc_info=True)