#!/usr/bin/env python3
"""
Test Production Script Integration
Verify that production scripts properly use the proven AtomicStoragePipeline
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add processing to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'courtlistener', 'processing', 'src'))

# Import proven production components
from processing.atomic_storage_pipeline import AtomicStoragePipeline
from processing.storage.gcs_connector import GCSConnector
from processing.storage.pinecone_connector import PineconeConnector
from processing.storage.neo4j_connector import Neo4jConnector
from processing.storage.supabase_connector import SupabaseConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_production_integration():
    """Test that production scripts can properly integrate with AtomicStoragePipeline"""
    logger.info("🧪 TESTING PRODUCTION SCRIPT INTEGRATION")
    logger.info("=" * 80)
    
    try:
        # Setup production clients using proven connectors
        logger.info("🔧 Setting up production database clients...")
        load_dotenv()
        
        supabase_connector = SupabaseConnector()
        gcs_connector = GCSConnector()
        pinecone_connector = PineconeConnector()
        neo4j_connector = Neo4jConnector()
        
        logger.info("✅ All production connectors initialized")
        
        # Initialize proven AtomicStoragePipeline with correct client integration
        logger.info("🏭 Initializing proven AtomicStoragePipeline...")
        storage_pipeline = AtomicStoragePipeline(
            supabase_client=supabase_connector.client,  # Use raw client
            gcs_client=gcs_connector,
            pinecone_client=pinecone_connector.index,  # Use raw Pinecone index
            neo4j_client=neo4j_connector.driver,  # Use raw Neo4j driver
            batch_size=5,  # Small batch for testing
            enable_legal_relationships=True
        )
        
        logger.info("✅ AtomicStoragePipeline initialized successfully")
        
        # Create test cases to verify integration
        logger.info("📝 Creating test cases for integration verification...")
        test_cases = []
        batch_id = f"integration_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        for i in range(5):
            case = {
                'id': f'integration_test_{batch_id}_{i}',
                'case_name': f'Integration Test Case {i+1}',
                'court': 'txs',
                'date_filed': '2025-07-31',
                'plain_text': f'This is integration test case {i+1} content for verifying production script integration with AtomicStoragePipeline.',
                'source': 'integration_test',
                'jurisdiction': 'tx',
                'doc_type': 'opinion',
                'practice_areas': ['integration_testing'],
                'created_at': datetime.now().isoformat(),
                'opinion_id': f'integration_test_{batch_id}_{i}',
                'author': f'Test Judge {i+1}',
                'type': 'integration_test'
            }
            test_cases.append(case)
        
        logger.info(f"✅ Created {len(test_cases)} test cases")
        
        # Store cases using AtomicStoragePipeline
        logger.info("💾 Testing storage via AtomicStoragePipeline...")
        result = await storage_pipeline.store_batch(test_cases, batch_id)
        
        if result.success:
            logger.info("✅ Integration test SUCCESSFUL!")
            logger.info(f"📊 Stored {result.total_items} cases across all systems")
            logger.info(f"📊 Storage results: {result.storage_results}")
            
            # Verify data was stored
            logger.info("🔍 Verifying data storage...")
            
            # Check Supabase
            supabase_response = supabase_connector.client.table('cases').select('id').eq('batch_id', batch_id).execute()
            supabase_count = len(supabase_response.data)
            logger.info(f"📊 Supabase: {supabase_count} cases stored")
            
            # Check Pinecone
            stats = pinecone_connector.index.describe_index_stats()
            logger.info(f"📊 Pinecone: {stats.total_vector_count} total vectors")
            
            # Check Neo4j
            with neo4j_connector.driver.session() as session:
                neo4j_result = session.run(f"MATCH (c:Case {{batch_id: '{batch_id}'}}) RETURN count(c) as count")
                neo4j_count = neo4j_result.single()["count"]
                logger.info(f"📊 Neo4j: {neo4j_count} case nodes stored")
            
            logger.info("🎉 PRODUCTION SCRIPT INTEGRATION TEST PASSED!")
            
            return {
                'success': True,
                'batch_id': batch_id,
                'cases_processed': len(test_cases),
                'storage_verification': {
                    'supabase': supabase_count,
                    'neo4j': neo4j_count,
                    'pinecone_total': stats.total_vector_count
                }
            }
        else:
            logger.error("❌ Integration test FAILED!")
            logger.error(f"Storage error: {result}")
            return {
                'success': False,
                'error': 'Storage pipeline failed',
                'storage_result': result
            }
            
    except Exception as e:
        logger.error(f"💥 Integration test crashed: {e}")
        return {
            'success': False,
            'error': str(e)
        }

async def main():
    """Main execution function"""
    try:
        # Run integration test
        results = await test_production_integration()
        
        # Final summary
        logger.info("🎉 PRODUCTION SCRIPT INTEGRATION TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Results: {results}")
        
        if results['success']:
            logger.info("✅ Production scripts successfully integrated with AtomicStoragePipeline!")
            sys.exit(0)
        else:
            logger.error("❌ Production script integration failed!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
