#!/usr/bin/env python3
"""
Enhanced Storage Integration Test
Test the integration of enhanced storage components with existing CourtListener data.

This test validates:
1. Basic storage connector functionality
2. Schema and table creation
3. Global UID tracking
4. Data consistency checks
5. Integration with existing CourtListener pipeline
"""

import sys
import os
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StorageIntegrationTest:
    """Integration test for enhanced storage system"""
    
    def __init__(self):
        """Initialize integration test"""
        self.results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'errors': []
        }
        
        print("Enhanced Storage Integration Test Initialized")
    
    def run_test(self, test_name: str, test_func, *args, **kwargs):
        """Run a test and record results"""
        self.results['total_tests'] += 1
        
        try:
            print(f"\n🧪 Running test: {test_name}")
            result = test_func(*args, **kwargs)
            
            if result:
                self.results['passed'] += 1
                print(f"✅ {test_name} PASSED")
                return True
            else:
                self.results['failed'] += 1
                print(f"❌ {test_name} FAILED")
                return False
            
        except Exception as e:
            self.results['failed'] += 1
            error_msg = f"Test error: {str(e)}"
            self.results['errors'].append(f"{test_name}: {error_msg}")
            print(f"💥 {test_name} ERROR: {e}")
            return False
    
    def test_file_structure(self):
        """Test that all required files exist"""
        try:
            required_files = [
                'courtlistener/processing/src/processing/storage/enhanced_storage_orchestrator.py',
                'courtlistener/processing/src/processing/storage/graphrag_storage_manager.py',
                'courtlistener/processing/src/processing/storage/enhanced_neo4j_connector.py',
                'courtlistener/processing/src/processing/storage/enhanced_storage_schema.sql'
            ]
            
            missing_files = []
            for file_path in required_files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)
                else:
                    file_size = os.path.getsize(file_path)
                    print(f"✓ {file_path} exists ({file_size:,} bytes)")
            
            if missing_files:
                print(f"Missing files: {', '.join(missing_files)}")
                return False
            
            print("All required storage files are present")
            return True
            
        except Exception as e:
            print(f"File structure test error: {e}")
            return False
    
    def test_existing_storage_connectors(self):
        """Test that existing storage connectors are available"""
        try:
            # Test Supabase connector availability
            supabase_file = 'courtlistener/processing/src/processing/storage/supabase_connector.py'
            if os.path.exists(supabase_file):
                print("✓ Supabase connector available")
            else:
                print("❌ Supabase connector missing")
                return False
            
            # Test Neo4j connector availability
            neo4j_file = 'courtlistener/processing/src/processing/storage/neo4j_connector.py'
            if os.path.exists(neo4j_file):
                print("✓ Neo4j connector available")
            else:
                print("❌ Neo4j connector missing")
                return False
            
            # Test Pinecone connector availability
            pinecone_file = 'courtlistener/processing/src/processing/storage/pinecone_connector.py'
            if os.path.exists(pinecone_file):
                print("✓ Pinecone connector available")
            else:
                print("❌ Pinecone connector missing")
                return False
            
            # Test GCS helper availability
            gcs_file = 'courtlistener/processing/src/processing/storage/gcs_helper.py'
            if os.path.exists(gcs_file):
                print("✓ GCS helper available")
            else:
                print("❌ GCS helper missing")
                return False
            
            return True
            
        except Exception as e:
            print(f"Storage connector test error: {e}")
            return False
    
    def test_sql_schema_validity(self):
        """Test that the SQL schema file is valid"""
        try:
            schema_file = 'courtlistener/processing/src/processing/storage/enhanced_storage_schema.sql'
            
            if not os.path.exists(schema_file):
                print("❌ Schema file missing")
                return False
            
            with open(schema_file, 'r') as f:
                schema_content = f.read()
            
            # Check for required tables
            required_tables = [
                'global_uid_registry',
                'graphrag_schemas',
                'etl_checkpoints',
                'storage_operations',
                'graphrag_entities',
                'graphrag_relationships'
            ]
            
            missing_tables = []
            for table in required_tables:
                if f"CREATE TABLE IF NOT EXISTS {table}" not in schema_content:
                    missing_tables.append(table)
                else:
                    print(f"✓ Table {table} defined in schema")
            
            if missing_tables:
                print(f"Missing table definitions: {', '.join(missing_tables)}")
                return False
            
            # Check for indexes
            if 'CREATE INDEX' in schema_content:
                print("✓ Indexes defined in schema")
            else:
                print("⚠️  No indexes found in schema")
            
            # Check for views
            if 'CREATE OR REPLACE VIEW' in schema_content:
                print("✓ Views defined in schema")
            else:
                print("⚠️  No views found in schema")
            
            print(f"Schema file is {len(schema_content):,} characters long")
            return True
            
        except Exception as e:
            print(f"SQL schema test error: {e}")
            return False
    
    def test_python_imports(self):
        """Test that Python files can be imported without syntax errors"""
        try:
            import ast
            
            python_files = [
                'courtlistener/processing/src/processing/storage/enhanced_storage_orchestrator.py',
                'courtlistener/processing/src/processing/storage/graphrag_storage_manager.py',
                'courtlistener/processing/src/processing/storage/enhanced_neo4j_connector.py'
            ]
            
            for file_path in python_files:
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Parse the Python file to check for syntax errors
                    ast.parse(content)
                    print(f"✓ {os.path.basename(file_path)} syntax is valid")
                    
                except SyntaxError as e:
                    print(f"❌ Syntax error in {file_path}: {e}")
                    return False
                except Exception as e:
                    print(f"❌ Error parsing {file_path}: {e}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"Python import test error: {e}")
            return False
    
    def test_configuration_compatibility(self):
        """Test compatibility with existing configuration files"""
        try:
            # Check for existing configuration files
            config_files = [
                'courtlistener/processing/src/config/enhanced_jurisdiction_config.json',
                'courtlistener/processing/src/config/document_taxonomy.json'
            ]
            
            existing_configs = []
            for config_file in config_files:
                if os.path.exists(config_file):
                    existing_configs.append(config_file)
                    print(f"✓ Configuration file exists: {config_file}")
                else:
                    print(f"⚠️  Configuration file missing: {config_file}")
            
            # Check for environment variable dependencies
            required_env_vars = [
                'SUPABASE_URL',
                'SUPABASE_SERVICE_ROLE_KEY',
                'NEO4J_URI',
                'NEO4J_PASSWORD',
                'PINECONE_API_KEY',
                'GCS_BUCKET_NAME'
            ]
            
            print("\nRequired environment variables:")
            for env_var in required_env_vars:
                if os.getenv(env_var):
                    print(f"✓ {env_var} is set")
                else:
                    print(f"⚠️  {env_var} is not set")
            
            return True  # Configuration compatibility is non-blocking
            
        except Exception as e:
            print(f"Configuration compatibility test error: {e}")
            return False
    
    def test_courtlistener_integration_points(self):
        """Test integration points with existing CourtListener processing"""
        try:
            # Check for existing CourtListener processing files
            integration_files = [
                'courtlistener/processing/src/processing/providers/court_listener.py',
                'courtlistener/processing/src/processing/providers/court_listener_api.py',
                'courtlistener/processing/src/processing/integrated_courtlistener_processor.py'
            ]
            
            existing_files = []
            for file_path in integration_files:
                if os.path.exists(file_path):
                    existing_files.append(file_path)
                    print(f"✓ Integration point exists: {file_path}")
                else:
                    print(f"⚠️  Integration point missing: {file_path}")
            
            # Check for enhanced judge extractor
            judge_extractor = 'courtlistener/processing/src/processing/enhanced_judge_extractor.py'
            if os.path.exists(judge_extractor):
                print(f"✓ Enhanced judge extractor available: {judge_extractor}")
            else:
                print(f"⚠️  Enhanced judge extractor missing: {judge_extractor}")
            
            return len(existing_files) >= 2  # At least 2 integration points should exist
            
        except Exception as e:
            print(f"CourtListener integration test error: {e}")
            return False
    
    def test_documentation_and_examples(self):
        """Test for documentation and example usage"""
        try:
            # Check for implementation plan
            impl_plan = 'ENHANCED_GRAPHRAG_IMPLEMENTATION_PLAN_2025-08-01.md'
            if os.path.exists(impl_plan):
                print(f"✓ Implementation plan available: {impl_plan}")
                
                with open(impl_plan, 'r') as f:
                    plan_content = f.read()
                
                if 'GraphRAG' in plan_content and 'storage' in plan_content.lower():
                    print("✓ Implementation plan contains GraphRAG storage documentation")
                else:
                    print("⚠️  Implementation plan may be incomplete")
            else:
                print(f"⚠️  Implementation plan missing: {impl_plan}")
            
            # Check for test files
            test_files = [
                'test_enhanced_storage_system.py',
                'test_enhanced_storage_integration.py'
            ]
            
            for test_file in test_files:
                if os.path.exists(test_file):
                    print(f"✓ Test file available: {test_file}")
                else:
                    print(f"⚠️  Test file missing: {test_file}")
            
            return True  # Documentation is helpful but not critical
            
        except Exception as e:
            print(f"Documentation test error: {e}")
            return False
    
    def test_mock_storage_operations(self):
        """Test mock storage operations to verify basic functionality"""
        try:
            # Create mock data structures
            mock_global_uid = str(uuid.uuid4())
            mock_entity = {
                'type': 'Judge',
                'name': 'Mock Judge',
                'properties': {
                    'court': 'Mock Court',
                    'jurisdiction': 'TX'
                }
            }
            
            mock_schema = {
                'node_types': [
                    {'label': 'Judge', 'properties': [{'name': 'name', 'type': 'STRING'}]}
                ],
                'relationship_types': [
                    {'label': 'PRESIDES_OVER', 'properties': []}
                ]
            }
            
            print(f"✓ Created mock global UID: {mock_global_uid}")
            print(f"✓ Created mock entity: {mock_entity['name']}")
            print(f"✓ Created mock schema with {len(mock_schema['node_types'])} node types")
            
            # Test data serialization
            serialized_entity = json.dumps(mock_entity)
            deserialized_entity = json.loads(serialized_entity)
            
            if deserialized_entity == mock_entity:
                print("✓ Data serialization works correctly")
            else:
                print("❌ Data serialization failed")
                return False
            
            # Test UID generation format
            test_uid = str(uuid.uuid5(uuid.NAMESPACE_DNS, "test:entity:123"))
            if len(test_uid) == 36 and test_uid.count('-') == 4:
                print("✓ UID generation format is correct")
            else:
                print("❌ UID generation format is incorrect")
                return False
            
            return True
            
        except Exception as e:
            print(f"Mock storage operations test error: {e}")
            return False
    
    def test_error_handling_structures(self):
        """Test that error handling structures are in place"""
        try:
            # Check error handling in enhanced storage orchestrator
            orchestrator_file = 'courtlistener/processing/src/processing/storage/enhanced_storage_orchestrator.py'
            
            with open(orchestrator_file, 'r') as f:
                content = f.read()
            
            error_handling_indicators = [
                'try:',
                'except Exception as e:',
                'logger.error',
                'rollback',
                'compensation'
            ]
            
            found_indicators = []
            for indicator in error_handling_indicators:
                if indicator in content:
                    found_indicators.append(indicator)
            
            print(f"✓ Found {len(found_indicators)}/{len(error_handling_indicators)} error handling indicators")
            
            if len(found_indicators) >= 4:
                print("✓ Comprehensive error handling appears to be implemented")
                return True
            else:
                print("⚠️  Error handling may be incomplete")
                return False
            
        except Exception as e:
            print(f"Error handling structures test error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all integration tests"""
        print("\n" + "="*80)
        print("🚀 ENHANCED STORAGE INTEGRATION TEST SUITE")
        print("="*80)
        
        tests = [
            ("File Structure", self.test_file_structure),
            ("Existing Storage Connectors", self.test_existing_storage_connectors),
            ("SQL Schema Validity", self.test_sql_schema_validity),
            ("Python Import Syntax", self.test_python_imports),
            ("Configuration Compatibility", self.test_configuration_compatibility),
            ("CourtListener Integration Points", self.test_courtlistener_integration_points),
            ("Documentation and Examples", self.test_documentation_and_examples),
            ("Mock Storage Operations", self.test_mock_storage_operations),
            ("Error Handling Structures", self.test_error_handling_structures)
        ]
        
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
        
        self.print_results()
        return self.results['failed'] == 0
    
    def print_results(self):
        """Print test results"""
        print("\n" + "="*80)
        print("📊 INTEGRATION TEST RESULTS")
        print("="*80)
        
        print(f"Total Tests: {self.results['total_tests']}")
        print(f"✅ Passed: {self.results['passed']}")
        print(f"❌ Failed: {self.results['failed']}")
        
        success_rate = (self.results['passed'] / self.results['total_tests']) * 100 if self.results['total_tests'] > 0 else 0
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if self.results['errors']:
            print(f"\n🔍 ERRORS:")
            for error in self.results['errors']:
                print(f"   • {error}")
        
        if self.results['failed'] == 0:
            print(f"\n🎉 ALL INTEGRATION TESTS PASSED!")
            print("The enhanced storage system is ready for integration with CourtListener data.")
        elif self.results['passed'] > self.results['failed']:
            print(f"\n⚠️  MOSTLY READY: {self.results['passed']}/{self.results['total_tests']} tests passed.")
            print("Minor issues detected, but system should work.")
        else:
            print(f"\n🚨 INTEGRATION ISSUES: Only {self.results['passed']}/{self.results['total_tests']} tests passed.")
            print("Significant issues detected. Please address before proceeding.")

def main():
    """Main test execution"""
    test_suite = StorageIntegrationTest()
    
    try:
        success = test_suite.run_all_tests()
        return success
    
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        return False
    
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)