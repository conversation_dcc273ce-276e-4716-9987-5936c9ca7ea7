#!/usr/bin/env python3
"""
Test CourtListener API Access
Simple test to verify API connectivity and permissions
"""

import asyncio
import aiohttp
import os
from dotenv import load_dotenv

async def test_courtlistener_api():
    """Test basic CourtListener API access"""
    load_dotenv()
    
    api_key = os.getenv('COURTLISTENER_API_KEY')
    print(f"API Key present: {'Yes' if api_key else 'No'}")
    print(f"API Key length: {len(api_key) if api_key else 0}")
    
    if not api_key:
        print("❌ No API key found")
        return
    
    headers = {
        'Authorization': f'Token {api_key}',
        'User-Agent': 'Texas-Laws-PersonalInjury/1.0'
    }
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Basic API access
        print("\n🧪 Testing basic API access...")
        try:
            async with session.get('https://www.courtlistener.com/api/rest/v4/', headers=headers) as response:
                print(f"API root status: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"Available endpoints: {list(data.keys())}")
                else:
                    print(f"Error response: {await response.text()}")
        except Exception as e:
            print(f"API root test failed: {e}")
        
        # Test 2: Search endpoint
        print("\n🔍 Testing search endpoint...")
        try:
            search_url = "https://www.courtlistener.com/api/rest/v4/search/"
            params = {
                'type': 'o',
                'format': 'json',
                'page_size': 1  # Just get 1 result
            }
            
            async with session.get(search_url, params=params, headers=headers) as response:
                print(f"Search status: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"Search results count: {data.get('count', 0)}")
                    print(f"Results returned: {len(data.get('results', []))}")
                    if data.get('results'):
                        result = data['results'][0]
                        print(f"Sample case: {result.get('caseName', 'Unknown')}")
                else:
                    error_text = await response.text()
                    print(f"Search error: {error_text}")
        except Exception as e:
            print(f"Search test failed: {e}")
        
        # Test 3: Opinions endpoint
        print("\n📄 Testing opinions endpoint...")
        try:
            opinions_url = "https://www.courtlistener.com/api/rest/v4/opinions/"
            params = {
                'format': 'json',
                'page_size': 1
            }
            
            async with session.get(opinions_url, params=params, headers=headers) as response:
                print(f"Opinions status: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"Opinions count: {data.get('count', 0)}")
                    if data.get('results'):
                        opinion = data['results'][0]
                        print(f"Sample opinion ID: {opinion.get('id')}")
                        print(f"Has plain text: {'Yes' if opinion.get('plain_text') else 'No'}")
                        if opinion.get('plain_text'):
                            text_length = len(opinion['plain_text'])
                            print(f"Text length: {text_length} characters")
                else:
                    error_text = await response.text()
                    print(f"Opinions error: {error_text}")
        except Exception as e:
            print(f"Opinions test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_courtlistener_api())
