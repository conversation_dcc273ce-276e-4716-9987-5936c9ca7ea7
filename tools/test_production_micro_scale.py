#!/usr/bin/env python3
"""
Production Micro-Scale Test (5 Cases)
Tests the production pipeline with real CourtListener API data and voyage-context-3 embeddings
"""

import asyncio
import logging
import os
import sys
import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

# Import working components
from supabase import create_client, Client
from pinecone import Pinecone
from neo4j import GraphDatabase
import aiohttp

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProductionMicroScaleTest:
    """
    Micro-scale production test with real data processing
    """
    
    def __init__(self):
        load_dotenv()
        
        # Initialize clients
        self.supabase = create_client(
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        )
        
        self.pinecone = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        self.pinecone_index = self.pinecone.Index(os.getenv('PINECONE_INDEX_NAME', 'texas-laws-voyage3large'))
        
        self.neo4j_driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'),
            auth=('neo4j', os.getenv('NEO4J_PASSWORD'))
        )
        
        self.courtlistener_api_key = os.getenv('COURTLISTENER_API_KEY')
        self.voyage_api_key = os.getenv('VOYAGE_API_KEY')
        self.voyage_model = os.getenv('VOYAGE_EMBEDDING_MODEL', 'voyage-context-3')
        
        self.stats = {
            'start_time': datetime.now(),
            'cases_processed': 0,
            'embeddings_generated': 0,
            'supabase_stored': 0,
            'pinecone_stored': 0,
            'neo4j_stored': 0,
            'errors': []
        }
    
    async def fetch_courtlistener_cases(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Fetch real cases from CourtListener API"""
        logger.info(f"🔍 Fetching {limit} cases from CourtListener API...")

        headers = {
            'Authorization': f'Token {self.courtlistener_api_key}',
            'User-Agent': 'Texas Laws Personal Injury Research'
        }

        # Fetch Texas personal injury cases
        url = 'https://www.courtlistener.com/api/rest/v4/opinions/'
        params = {
            'court__jurisdiction': 'F',  # Federal courts
            'court__in': 'txed,txnd,txsd,txwd',  # Texas federal districts
            'page_size': limit,
            'order_by': '-date_created'
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    cases = data.get('results', [])[:limit]  # Ensure we only get the requested number
                    logger.info(f"✅ Fetched {len(cases)} cases from CourtListener")
                    return cases
                else:
                    logger.error(f"❌ CourtListener API error: {response.status}")
                    return []
    
    async def generate_voyage_embedding(self, text: str) -> List[float]:
        """Generate embedding using voyage-context-3"""
        if not self.voyage_api_key:
            logger.warning("No Voyage API key, using mock embedding")
            return [0.1] * 1024
        
        headers = {
            'Authorization': f'Bearer {self.voyage_api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'input': [text[:8000]],  # Limit text length
            'model': self.voyage_model
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'https://api.voyageai.com/v1/embeddings',
                headers=headers,
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    embedding = data['data'][0]['embedding']
                    logger.info(f"✅ Generated {len(embedding)}-dimensional embedding")
                    return embedding
                else:
                    logger.error(f"❌ Voyage API error: {response.status}")
                    return [0.1] * 1024
    
    async def store_in_supabase(self, case: Dict[str, Any]) -> bool:
        """Store case in Supabase"""
        try:
            case_data = {
                'id': str(case.get('id')),
                'case_name': case.get('case_name', ''),
                'court': case.get('court', ''),
                'date_filed': case.get('date_filed'),
                'plain_text': case.get('plain_text', '')[:10000],  # Limit text
                'source': 'courtlistener',
                'jurisdiction': 'tx',
                'created_at': datetime.now().isoformat()
            }
            
            result = self.supabase.table('cases').upsert(case_data).execute()
            logger.info(f"✅ Stored case {case_data['id']} in Supabase")
            return True
        except Exception as e:
            logger.error(f"❌ Supabase storage error: {e}")
            return False
    
    async def store_in_pinecone(self, case: Dict[str, Any], embedding: List[float]) -> bool:
        """Store case vector in Pinecone"""
        try:
            vector_id = f"case_{case.get('id')}"
            metadata = {
                'case_id': str(case.get('id')),
                'case_name': case.get('case_name', ''),
                'court': case.get('court', ''),
                'date_filed': case.get('date_filed'),
                'source': 'courtlistener',
                'jurisdiction': 'tx',
                'embedding_model': self.voyage_model
            }
            
            self.pinecone_index.upsert(
                vectors=[(vector_id, embedding, metadata)],
                namespace='tx-personal_injury'
            )
            logger.info(f"✅ Stored vector {vector_id} in Pinecone")
            return True
        except Exception as e:
            logger.error(f"❌ Pinecone storage error: {e}")
            return False
    
    async def store_in_neo4j(self, case: Dict[str, Any]) -> bool:
        """Store case in Neo4j"""
        try:
            with self.neo4j_driver.session() as session:
                query = """
                MERGE (c:Case {id: $case_id})
                SET c.case_name = $case_name,
                    c.court = $court,
                    c.date_filed = $date_filed,
                    c.source = $source,
                    c.jurisdiction = $jurisdiction,
                    c.created_at = $created_at
                RETURN c
                """
                
                result = session.run(query, {
                    'case_id': str(case.get('id')),
                    'case_name': case.get('case_name', ''),
                    'court': case.get('court', ''),
                    'date_filed': case.get('date_filed'),
                    'source': 'courtlistener',
                    'jurisdiction': 'tx',
                    'created_at': datetime.now().isoformat()
                })
                
                logger.info(f"✅ Stored case {case.get('id')} in Neo4j")
                return True
        except Exception as e:
            logger.error(f"❌ Neo4j storage error: {e}")
            return False
    
    async def process_case(self, case: Dict[str, Any]) -> Dict[str, bool]:
        """Process a single case through the full pipeline"""
        case_id = case.get('id')
        logger.info(f"🔄 Processing case {case_id}: {case.get('case_name', 'Unknown')}")
        
        results = {
            'embedding_generated': False,
            'supabase_stored': False,
            'pinecone_stored': False,
            'neo4j_stored': False
        }
        
        try:
            # Generate embedding
            text = case.get('plain_text', '') or case.get('html_with_citations', '')
            if text:
                embedding = await self.generate_voyage_embedding(text)
                results['embedding_generated'] = len(embedding) == 1024
                self.stats['embeddings_generated'] += 1
            else:
                logger.warning(f"⚠️ No text content for case {case_id}")
                embedding = [0.1] * 1024
            
            # Store in all systems
            results['supabase_stored'] = await self.store_in_supabase(case)
            if results['supabase_stored']:
                self.stats['supabase_stored'] += 1
            
            results['pinecone_stored'] = await self.store_in_pinecone(case, embedding)
            if results['pinecone_stored']:
                self.stats['pinecone_stored'] += 1
            
            results['neo4j_stored'] = await self.store_in_neo4j(case)
            if results['neo4j_stored']:
                self.stats['neo4j_stored'] += 1
            
            self.stats['cases_processed'] += 1
            logger.info(f"✅ Case {case_id} processing completed")
            
        except Exception as e:
            logger.error(f"❌ Error processing case {case_id}: {e}")
            self.stats['errors'].append(str(e))
        
        return results

    async def run_micro_scale_test(self) -> Dict[str, Any]:
        """Run the micro-scale test with 5 cases"""
        logger.info("🚀 Starting Production Micro-Scale Test (5 Cases)")
        logger.info("=" * 60)

        try:
            # Fetch cases
            cases = await self.fetch_courtlistener_cases(5)
            if not cases:
                return {'success': False, 'error': 'No cases fetched'}

            # Process each case
            all_results = []
            for case in cases:
                case_results = await self.process_case(case)
                all_results.append(case_results)

            # Calculate success metrics
            total_cases = len(cases)
            successful_embeddings = sum(1 for r in all_results if r['embedding_generated'])
            successful_supabase = sum(1 for r in all_results if r['supabase_stored'])
            successful_pinecone = sum(1 for r in all_results if r['pinecone_stored'])
            successful_neo4j = sum(1 for r in all_results if r['neo4j_stored'])

            # Check success criteria
            success = (
                total_cases == 5 and
                successful_embeddings == 5 and
                successful_supabase == 5 and
                successful_pinecone == 5 and
                successful_neo4j == 5
            )

            self.stats['end_time'] = datetime.now()
            duration = self.stats['end_time'] - self.stats['start_time']

            results = {
                'success': success,
                'total_cases': total_cases,
                'successful_embeddings': successful_embeddings,
                'successful_supabase': successful_supabase,
                'successful_pinecone': successful_pinecone,
                'successful_neo4j': successful_neo4j,
                'duration_seconds': duration.total_seconds(),
                'voyage_model': self.voyage_model,
                'embedding_dimensions': 1024,
                'stats': self.stats
            }

            # Print results
            logger.info("\n📊 MICRO-SCALE TEST RESULTS")
            logger.info("=" * 40)
            logger.info(f"✅ Success: {success}")
            logger.info(f"📄 Cases processed: {total_cases}/5")
            logger.info(f"🧠 Embeddings generated: {successful_embeddings}/5 (voyage-context-3, 1024 dims)")
            logger.info(f"🗄️ Supabase stored: {successful_supabase}/5")
            logger.info(f"🔍 Pinecone stored: {successful_pinecone}/5")
            logger.info(f"🕸️ Neo4j stored: {successful_neo4j}/5")
            logger.info(f"⏱️ Duration: {duration.total_seconds():.2f} seconds")
            logger.info(f"❌ Errors: {len(self.stats['errors'])}")

            if success:
                logger.info("🎉 MICRO-SCALE TEST PASSED - All success criteria met!")
            else:
                logger.error("❌ MICRO-SCALE TEST FAILED - Review errors above")

            return results

        except Exception as e:
            logger.error(f"💥 Test failed with error: {e}")
            return {'success': False, 'error': str(e)}

        finally:
            # Cleanup
            self.neo4j_driver.close()


async def main():
    """Main test execution"""
    test = ProductionMicroScaleTest()
    results = await test.run_micro_scale_test()

    # Save results
    results_file = f"test_artifacts/micro_scale_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    logger.info(f"📄 Results saved to: {results_file}")

    # Exit with appropriate code
    sys.exit(0 if results.get('success', False) else 1)


if __name__ == "__main__":
    asyncio.run(main())
