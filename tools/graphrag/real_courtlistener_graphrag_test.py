#!/usr/bin/env python3
"""
Real CourtListener GraphRAG Test
Fetch actual full-text legal documents and test GraphRAG processing
"""

import asyncio
import logging
import os
import sys
import json
import time
import aiohttp
from datetime import datetime
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# Add processing to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'courtlistener', 'processing', 'src'))

from processing.enhanced_judge_extractor import EnhancedJudgeExtractor, GraphRAGConfig
from processing.cost_monitor import CostLimits

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealCourtListenerGraphRAGTest:
    """
    Test GraphRAG with real CourtListener full-text documents
    Simple approach - fetch real cases and process them
    """
    
    def __init__(self):
        """Initialize real CourtListener test"""
        load_dotenv()
        
        self.api_key = os.getenv('COURTLISTENER_API_KEY')
        if not self.api_key:
            raise ValueError("COURTLISTENER_API_KEY not found in environment")
        
        # Configure GraphRAG for real document processing
        self.graphrag_config = GraphRAGConfig(
            model_name="gemini-2.0-flash",
            max_tokens=4000,
            temperature=0.0,
            cost_per_token=0.000075,
            max_cost_per_case=1.00,  # Allow higher cost for real testing
            enable_entity_resolution=True,
            batch_size=5
        )
        
        # Cost monitoring (not blocking)
        cost_limits = CostLimits(
            daily_budget=200.0,
            hourly_budget=50.0,
            cost_per_case_limit=1.00,
            token_rate_limit=1000000,
            request_rate_limit=500
        )
        
        self.enhanced_extractor = EnhancedJudgeExtractor(self.graphrag_config)
        self.enhanced_extractor.cost_monitor.limits = cost_limits
        
        # Test results
        self.results = {
            'test_id': f"real_cl_graphrag_{int(datetime.now().timestamp())}",
            'start_time': datetime.now().isoformat(),
            'cases_processed': 0,
            'successful_extractions': 0,
            'total_cost': 0.0,
            'graphrag_successes': 0,
            'pattern_successes': 0,
            'api_successes': 0,
            'detailed_cases': []
        }
        
        logger.info("Real CourtListener GraphRAG Test initialized")
        logger.info(f"Vertex AI API should now be enabled in project")
    
    async def run_real_test(self, num_cases: int = 10) -> Dict[str, Any]:
        """Run test with real CourtListener documents"""
        logger.info("🚀 REAL COURTLISTENER GRAPHRAG TEST")
        logger.info("=" * 80)
        logger.info(f"Fetching {num_cases} real CourtListener cases with full text")
        logger.info("Processing with GraphRAG enabled")
        logger.info("")
        
        start_time = time.time()
        
        try:
            # Fetch real CourtListener cases
            real_cases = await self._fetch_real_courtlistener_cases(num_cases)
            if not real_cases:
                logger.error("No real cases fetched - aborting test")
                return self.results
            
            logger.info(f"Successfully fetched {len(real_cases)} real cases")
            
            # Process each case with GraphRAG
            await self._process_real_cases(real_cases)
            
            # Analyze results
            self._analyze_results()
            
            total_time = time.time() - start_time
            self.results['total_time'] = total_time
            
            # Save results
            await self._save_results()
            
            return self.results
            
        except Exception as e:
            logger.error(f"Real test failed: {e}")
            import traceback
            traceback.print_exc()
            return self.results
    
    async def _fetch_real_courtlistener_cases(self, num_cases: int) -> List[Dict[str, Any]]:
        """Fetch real cases from CourtListener API with full text"""
        logger.info("📡 FETCHING REAL COURTLISTENER CASES")
        logger.info("-" * 50)
        
        cases = []
        
        async with aiohttp.ClientSession() as session:
            # Directly fetch opinions (simpler approach)
            opinions_url = "https://www.courtlistener.com/api/rest/v4/opinions/"

            params = {
                'format': 'json',
                'page_size': 50,  # Get more options to find good cases
                'ordering': '-date_created'  # Recent opinions
            }
            
            headers = {
                'Authorization': f'Token {self.api_key}',
                'User-Agent': 'Texas-Laws-PersonalInjury/1.0'
            }
            
            try:
                logger.info(f"Fetching opinions from CourtListener...")
                async with session.get(opinions_url, params=params, headers=headers) as response:
                    if response.status != 200:
                        logger.error(f"Opinions fetch failed: {response.status}")
                        error_text = await response.text()
                        logger.error(f"Error details: {error_text}")
                        return []

                    opinions_data = await response.json()
                    results = opinions_data.get('results', [])

                    logger.info(f"Found {len(results)} opinions")

                    # Process each opinion directly
                    for i, opinion_data in enumerate(results[:num_cases * 3]):  # Get more to filter
                        try:
                            case_id = opinion_data.get('id')
                            if not case_id:
                                continue

                            # Check if we have substantial text content
                            plain_text = opinion_data.get('plain_text', '')

                            if plain_text and len(plain_text) > 1000:  # At least 1000 characters
                                logger.info(f"Processing opinion {case_id}: {len(plain_text)} characters")

                                # Get cluster info for case name
                                cluster_id = opinion_data.get('cluster')
                                case_name = "Unknown Case"
                                court_id = "unknown"
                                date_filed = None

                                if cluster_id:
                                    # Fetch cluster details for case name
                                    cluster_url = f"https://www.courtlistener.com/api/rest/v4/clusters/{cluster_id}/"
                                    try:
                                        async with session.get(cluster_url, headers=headers) as cluster_response:
                                            if cluster_response.status == 200:
                                                cluster_data = await cluster_response.json()
                                                case_name = cluster_data.get('case_name', 'Unknown Case')
                                                court_id = cluster_data.get('docket', {}).get('court', 'unknown') if isinstance(cluster_data.get('docket'), dict) else 'unknown'
                                                date_filed = cluster_data.get('date_filed')
                                    except Exception as e:
                                        logger.warning(f"Failed to fetch cluster {cluster_id}: {e}")

                                # Create case data structure
                                case_data = {
                                    'id': f"cl_real_{case_id}",
                                    'source_id': case_id,
                                    'case_name': case_name,
                                    'court_id': court_id,
                                    'date_filed': date_filed,
                                    'source': 'courtlistener',
                                    'plain_text': plain_text,
                                    'html_content': opinion_data.get('html_with_citations', ''),
                                    'author_id': opinion_data.get('author'),
                                    'panel_ids': opinion_data.get('panel', []),
                                    'word_count': len(plain_text.split()),
                                    'url': f"https://www.courtlistener.com/opinion/{case_id}/"
                                }

                                cases.append(case_data)
                                logger.info(f"  ✅ Added: {case_name[:60]}... ({len(plain_text)} chars)")

                                # Stop when we have enough cases
                                if len(cases) >= num_cases:
                                    break
                            else:
                                logger.debug(f"  ⏭️ Opinion {case_id}: Insufficient text content ({len(plain_text)} chars)")

                            # Rate limiting
                            await asyncio.sleep(0.1)  # 10 requests per second

                        except Exception as e:
                            logger.error(f"Error processing opinion {case_id}: {e}")
                            continue
                    
            except Exception as e:
                logger.error(f"Failed to search CourtListener: {e}")
                return []
        
        logger.info(f"Successfully fetched {len(cases)} real cases with substantial content")
        return cases
    
    async def _process_real_cases(self, real_cases: List[Dict[str, Any]]):
        """Process real cases with GraphRAG"""
        logger.info("🧠 PROCESSING REAL CASES WITH GRAPHRAG")
        logger.info("-" * 50)
        
        for i, case_data in enumerate(real_cases, 1):
            case_id = case_data.get('id')
            case_name = case_data.get('case_name', 'Unknown')
            word_count = case_data.get('word_count', 0)
            
            try:
                logger.info(f"Processing case {i}/{len(real_cases)}: {case_id}")
                logger.info(f"  📄 Case: {case_name[:80]}...")
                logger.info(f"  📊 Word count: {word_count:,}")
                logger.info(f"  🏛️ Court: {case_data.get('court_id')}")
                
                # Process with enhanced extractor (includes GraphRAG)
                start_time = time.time()
                judges, metrics = await self.enhanced_extractor.extract_judges_enhanced(case_data)
                processing_time = time.time() - start_time
                
                # Calculate total cost for this case
                case_cost = sum(m.token_cost for m in metrics.values())
                self.results['total_cost'] += case_cost
                
                # Record detailed results
                case_result = {
                    'case_id': case_id,
                    'case_name': case_name,
                    'court_id': case_data.get('court_id'),
                    'word_count': word_count,
                    'judges_found': len(judges),
                    'judges': [
                        {
                            'name': j.name,
                            'confidence': j.confidence,
                            'method': j.extraction_method,
                            'role': getattr(j, 'role', None),
                            'court': getattr(j, 'court', None)
                        } for j in judges
                    ],
                    'processing_time': processing_time,
                    'cost': case_cost,
                    'metrics': {k: {
                        'method': v.method_name,
                        'judges_found': v.judges_found,
                        'confidence_avg': v.confidence_avg,
                        'token_cost': v.token_cost
                    } for k, v in metrics.items()},
                    'url': case_data.get('url')
                }
                
                self.results['detailed_cases'].append(case_result)
                self.results['cases_processed'] += 1
                
                if judges:
                    self.results['successful_extractions'] += 1
                    logger.info(f"  ✅ Found {len(judges)} judges:")
                    
                    for judge in judges:
                        logger.info(f"    - {judge.name} (confidence: {judge.confidence:.2f}, method: {judge.extraction_method})")
                        
                        # Track method success
                        if 'graphrag' in judge.extraction_method:
                            self.results['graphrag_successes'] += 1
                        elif 'api' in judge.extraction_method:
                            self.results['api_successes'] += 1
                        else:
                            self.results['pattern_successes'] += 1
                else:
                    logger.info(f"  ❌ No judges found")
                
                logger.info(f"  💰 Case cost: ${case_cost:.4f}")
                logger.info(f"  ⏱️ Processing time: {processing_time:.2f}s")
                logger.info(f"  💰 Total cost so far: ${self.results['total_cost']:.4f}")
                
                # Progress update
                if i % 3 == 0:
                    success_rate = self.results['successful_extractions'] / self.results['cases_processed']
                    avg_cost = self.results['total_cost'] / self.results['cases_processed']
                    logger.info(f"📊 Progress: {i} cases, {success_rate:.1%} success, ${avg_cost:.4f} avg cost")
                
            except Exception as e:
                logger.error(f"Failed to process case {case_id}: {e}")
                continue
        
        logger.info("🏁 REAL CASE PROCESSING COMPLETED")
    
    def _analyze_results(self):
        """Analyze results from real CourtListener testing"""
        logger.info("📊 ANALYZING REAL COURTLISTENER RESULTS")
        logger.info("-" * 50)
        
        if self.results['cases_processed'] == 0:
            logger.error("No cases were processed")
            return
        
        # Overall metrics
        success_rate = self.results['successful_extractions'] / self.results['cases_processed']
        avg_cost = self.results['total_cost'] / self.results['cases_processed']
        total_judges = sum(case['judges_found'] for case in self.results['detailed_cases'])
        avg_judges_per_case = total_judges / self.results['cases_processed']
        
        logger.info(f"📈 OVERALL PERFORMANCE:")
        logger.info(f"  Cases Processed: {self.results['cases_processed']}")
        logger.info(f"  Success Rate: {success_rate:.1%}")
        logger.info(f"  Total Judges Found: {total_judges}")
        logger.info(f"  Avg Judges/Case: {avg_judges_per_case:.2f}")
        logger.info(f"  Total Cost: ${self.results['total_cost']:.4f}")
        logger.info(f"  Avg Cost/Case: ${avg_cost:.4f}")
        
        # Method breakdown
        logger.info(f"📊 METHOD BREAKDOWN:")
        logger.info(f"  GraphRAG Successes: {self.results['graphrag_successes']}")
        logger.info(f"  Pattern Successes: {self.results['pattern_successes']}")
        logger.info(f"  API Successes: {self.results['api_successes']}")
        
        # GraphRAG specific analysis
        graphrag_cases = [case for case in self.results['detailed_cases'] 
                         if any('graphrag' in judge['method'] for judge in case['judges'])]
        
        if graphrag_cases:
            logger.info(f"🧠 GRAPHRAG PERFORMANCE:")
            logger.info(f"  Cases with GraphRAG extractions: {len(graphrag_cases)}")
            graphrag_cost = sum(case['cost'] for case in graphrag_cases)
            logger.info(f"  GraphRAG total cost: ${graphrag_cost:.4f}")
            logger.info(f"  GraphRAG avg cost: ${graphrag_cost/len(graphrag_cases):.4f}")
        else:
            logger.info("🧠 GRAPHRAG PERFORMANCE: No GraphRAG extractions found")
        
        # Cost analysis
        costs = [case['cost'] for case in self.results['detailed_cases']]
        if costs:
            logger.info(f"💰 COST ANALYSIS:")
            logger.info(f"  Min cost: ${min(costs):.4f}")
            logger.info(f"  Max cost: ${max(costs):.4f}")
            logger.info(f"  Avg cost: ${avg_cost:.4f}")
    
    async def _save_results(self):
        """Save test results"""
        results_file = f"real_cl_graphrag_results_{int(datetime.now().timestamp())}.json"
        
        self.results.update({
            'end_time': datetime.now().isoformat(),
            'extractor_metrics': self.enhanced_extractor.get_metrics_summary(),
            'cost_monitor_metrics': self.enhanced_extractor.cost_monitor.get_current_metrics()
        })
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        logger.info(f"Results saved to {results_file}")

async def main():
    """Main test execution"""
    logger.info("🎯 REAL COURTLISTENER GRAPHRAG TEST")
    logger.info("=" * 80)
    logger.info("Testing GraphRAG with real CourtListener full-text documents")
    logger.info("Vertex AI API should now be enabled")
    logger.info("")
    
    try:
        test = RealCourtListenerGraphRAGTest()
        results = await test.run_real_test(num_cases=8)  # Test with 8 real cases
        
        logger.info("")
        logger.info("🏆 REAL COURTLISTENER TEST SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Cases Processed: {results.get('cases_processed', 0)}")
        logger.info(f"Successful Extractions: {results.get('successful_extractions', 0)}")
        logger.info(f"GraphRAG Successes: {results.get('graphrag_successes', 0)}")
        logger.info(f"Total Cost: ${results.get('total_cost', 0.0):.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"💥 Real test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
