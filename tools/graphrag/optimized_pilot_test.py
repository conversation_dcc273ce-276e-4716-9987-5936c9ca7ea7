#!/usr/bin/env python3
"""
Optimized Neo4j GraphRAG Pilot Test - Phase 2
Implements cost optimization strategies based on Phase 1 results
"""

import asyncio
import logging
import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, List, Any
from dotenv import load_dotenv

# Add processing to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'courtlistener', 'processing', 'src'))

from processing.enhanced_judge_extractor import EnhancedJudgeExtractor
from processing.storage.supabase_connector import SupabaseConnector
from optimized_graphrag_config import (
    OptimizedGraphRAGConfig, 
    OptimizedLegalSchema, 
    CostOptimizationStrategies,
    STAGING_CONFIG
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OptimizedGraphRAGPilot:
    """
    Optimized pilot test with cost controls and efficiency improvements
    """
    
    def __init__(self, config: OptimizedGraphRAGConfig = None):
        """Initialize optimized pilot"""
        load_dotenv()
        
        self.config = config or STAGING_CONFIG
        self.supabase = SupabaseConnector()
        
        # Initialize enhanced extractor with optimized config
        self.enhanced_extractor = EnhancedJudgeExtractor(self.config)
        
        # Results tracking
        self.results = {
            'phase': 'optimization',
            'config_used': self.config.__dict__,
            'cases_processed': 0,
            'successful_extractions': 0,
            'cost_savings': 0.0,
            'performance_improvements': {}
        }
        
        logger.info("Optimized GraphRAG Pilot initialized with cost controls")
    
    async def run_optimized_pilot(self) -> Dict[str, Any]:
        """Run optimized pilot test with cost controls"""
        logger.info("🚀 PHASE 2: OPTIMIZED GRAPHRAG PILOT TEST")
        logger.info("=" * 80)
        
        start_time = time.time()
        
        try:
            # Create optimized test cases
            test_cases = self._create_optimized_test_cases()
            
            # Run extraction with optimization strategies
            await self._run_optimized_extractions(test_cases)
            
            # Analyze cost improvements
            cost_analysis = self._analyze_cost_improvements()
            
            # Generate final recommendations
            recommendations = self._generate_final_recommendations(cost_analysis)
            
            total_time = time.time() - start_time
            
            # Save results
            await self._save_optimized_results(recommendations, total_time)
            
            return self.results
            
        except Exception as e:
            logger.error(f"Optimized pilot failed: {e}")
            import traceback
            traceback.print_exc()
            return self.results
    
    def _create_optimized_test_cases(self) -> List[Dict[str, Any]]:
        """Create test cases optimized for cost-effective processing"""
        
        # Shorter, more focused test cases
        optimized_cases = [
            {
                'id': f'optimized_test_1_{int(time.time())}',
                'case_name': 'Optimized Test Case 1',
                'court_id': 'ca5',
                'source': 'courtlistener',
                'plain_text': '''Before JONES, SMITH, and ELROD, Circuit Judges.
                
JONES, Circuit Judge:
This personal injury case involves workplace safety. We affirm the district court's judgment.

SMITH, Circuit Judge, concurring:
I agree with the majority but write separately on the safety regulation issue.'''
            },
            {
                'id': f'optimized_test_2_{int(time.time())}',
                'case_name': 'Optimized Test Case 2',
                'court_id': 'txsd',
                'source': 'courtlistener',
                'plain_text': '''RODRIGUEZ, District Judge:
This matter comes before the Court on defendant's motion for summary judgment.
The Court GRANTS the motion for the reasons set forth below.'''
            },
            {
                'id': f'optimized_test_3_{int(time.time())}',
                'case_name': 'Optimized Test Case 3',
                'court_id': 'ca5',
                'source': 'courtlistener',
                'plain_text': '''Before HIGGINBOTHAM, Chief Judge, STEWART and WILLETT, Circuit Judges.

HIGGINBOTHAM, Chief Judge:
We reverse the district court's ruling on insurance coverage.

STEWART, Circuit Judge, concurring:
I concur but note the need for clearer policy language.'''
            }
        ]
        
        logger.info(f"Created {len(optimized_cases)} optimized test cases")
        return optimized_cases
    
    async def _run_optimized_extractions(self, test_cases: List[Dict[str, Any]]):
        """Run extractions with optimization strategies"""
        logger.info("🔧 RUNNING OPTIMIZED EXTRACTIONS")
        logger.info("-" * 50)
        
        total_cost_before = 0.0
        total_cost_after = 0.0
        
        for i, case_data in enumerate(test_cases, 1):
            case_id = case_data.get('id')
            
            try:
                logger.info(f"Processing optimized case {i}/{len(test_cases)}: {case_id}")
                
                # Check if case should be processed with GraphRAG
                should_process = CostOptimizationStrategies.should_process_with_graphrag(
                    case_data, self.config
                )
                
                if not should_process:
                    logger.info(f"  ⏭️ Skipping GraphRAG processing (cost optimization)")
                    continue
                
                # Preprocess text for efficiency
                original_text = case_data.get('plain_text', '')
                optimized_text = CostOptimizationStrategies.preprocess_text_for_efficiency(
                    original_text, self.config
                )
                
                # Calculate cost savings from preprocessing
                original_tokens = len(original_text.split()) * 1.3
                optimized_tokens = len(optimized_text.split()) * 1.3
                cost_before = original_tokens * self.config.cost_per_token
                cost_after = optimized_tokens * self.config.cost_per_token
                
                total_cost_before += cost_before
                total_cost_after += cost_after
                
                logger.info(f"  💰 Cost optimization: ${cost_before:.4f} → ${cost_after:.4f} ({((cost_before-cost_after)/cost_before*100):.1f}% savings)")
                
                # Update case data with optimized text
                case_data['plain_text'] = optimized_text
                
                # Run enhanced extraction
                judges, metrics = await self.enhanced_extractor.extract_judges_enhanced(case_data)
                
                self.results['cases_processed'] += 1
                
                if judges:
                    self.results['successful_extractions'] += 1
                    logger.info(f"  ✅ Found {len(judges)} judges:")
                    for judge in judges:
                        logger.info(f"    - {judge.name} (confidence: {judge.confidence:.2f})")
                else:
                    logger.info(f"  ❌ No judges found")
                
            except Exception as e:
                logger.error(f"Failed to process case {case_id}: {e}")
                continue
        
        # Calculate total cost savings
        self.results['cost_savings'] = total_cost_before - total_cost_after
        savings_percentage = (self.results['cost_savings'] / total_cost_before * 100) if total_cost_before > 0 else 0
        
        logger.info(f"💰 TOTAL COST OPTIMIZATION:")
        logger.info(f"  Before: ${total_cost_before:.4f}")
        logger.info(f"  After: ${total_cost_after:.4f}")
        logger.info(f"  Savings: ${self.results['cost_savings']:.4f} ({savings_percentage:.1f}%)")
    
    def _analyze_cost_improvements(self) -> Dict[str, Any]:
        """Analyze cost improvements from optimization"""
        
        # Get cost analysis from optimization strategies
        cost_analysis = CostOptimizationStrategies.get_cost_analysis(
            self.results['cases_processed'],
            self.enhanced_extractor.cost_monitor.metrics.total_cost
        )
        
        # Compare with Phase 1 results
        phase1_avg_cost = 0.0207  # From pilot results
        current_avg_cost = cost_analysis['avg_cost_per_case']
        cost_improvement = ((phase1_avg_cost - current_avg_cost) / phase1_avg_cost * 100) if phase1_avg_cost > 0 else 0
        
        cost_analysis.update({
            'phase1_comparison': {
                'phase1_avg_cost': phase1_avg_cost,
                'current_avg_cost': current_avg_cost,
                'improvement_percentage': cost_improvement,
                'meets_target': current_avg_cost <= 0.01
            }
        })
        
        logger.info(f"📊 COST ANALYSIS:")
        logger.info(f"  Phase 1 avg cost: ${phase1_avg_cost:.4f}")
        logger.info(f"  Current avg cost: ${current_avg_cost:.4f}")
        logger.info(f"  Improvement: {cost_improvement:.1f}%")
        logger.info(f"  Meets target (<$0.01): {'✅' if cost_analysis['phase1_comparison']['meets_target'] else '❌'}")
        
        return cost_analysis
    
    def _generate_final_recommendations(self, cost_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final recommendations for production deployment"""
        
        meets_cost_target = cost_analysis['phase1_comparison']['meets_target']
        success_rate = self.results['successful_extractions'] / max(self.results['cases_processed'], 1)
        
        recommendations = {
            'proceed_to_production': False,
            'deployment_readiness': 'not_ready',
            'cost_optimization_success': meets_cost_target,
            'accuracy_maintained': success_rate >= 0.85,
            'next_steps': []
        }
        
        if meets_cost_target and success_rate >= 0.85:
            recommendations.update({
                'proceed_to_production': True,
                'deployment_readiness': 'ready',
                'recommended_config': 'STAGING_CONFIG',
                'next_steps': [
                    'Deploy to staging environment with optimized configuration',
                    'Run extended testing with 1,000 real cases',
                    'Monitor cost and performance metrics',
                    'Proceed to production after successful staging validation'
                ]
            })
            logger.info("✅ RECOMMENDATION: PROCEED TO STAGING DEPLOYMENT")
            
        elif meets_cost_target:
            recommendations.update({
                'deployment_readiness': 'needs_accuracy_improvement',
                'next_steps': [
                    'Improve text preprocessing to maintain accuracy',
                    'Adjust schema for better judge extraction',
                    'Retry pilot with accuracy optimizations'
                ]
            })
            logger.info("⚠️ RECOMMENDATION: IMPROVE ACCURACY BEFORE DEPLOYMENT")
            
        else:
            recommendations.update({
                'deployment_readiness': 'needs_cost_optimization',
                'next_steps': [
                    'Further reduce token usage through advanced preprocessing',
                    'Consider alternative models or hybrid approaches',
                    'Implement more selective case processing'
                ]
            })
            logger.info("❌ RECOMMENDATION: FURTHER COST OPTIMIZATION NEEDED")
        
        return recommendations
    
    async def _save_optimized_results(self, recommendations: Dict[str, Any], total_time: float):
        """Save optimized pilot results"""
        
        final_results = {
            'test_id': f"optimized_graphrag_pilot_{int(datetime.now().timestamp())}",
            'timestamp': datetime.now().isoformat(),
            'phase': 'optimization',
            'total_time': total_time,
            'configuration': self.config.__dict__,
            'results': self.results,
            'cost_analysis': recommendations,
            'extractor_metrics': self.enhanced_extractor.get_metrics_summary()
        }
        
        # Save to file
        results_file = f"optimized_pilot_results_{int(datetime.now().timestamp())}.json"
        with open(results_file, 'w') as f:
            json.dump(final_results, f, indent=2, default=str)
        
        logger.info(f"Optimized pilot results saved to {results_file}")

async def main():
    """Main optimized pilot execution"""
    logger.info("🎯 NEO4J GRAPHRAG OPTIMIZATION PILOT - PHASE 2")
    logger.info("=" * 80)
    
    try:
        pilot = OptimizedGraphRAGPilot(STAGING_CONFIG)
        results = await pilot.run_optimized_pilot()
        
        logger.info("")
        logger.info("🏆 OPTIMIZATION PILOT SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Cases Processed: {results.get('cases_processed', 0)}")
        logger.info(f"Successful Extractions: {results.get('successful_extractions', 0)}")
        logger.info(f"Cost Savings: ${results.get('cost_savings', 0.0):.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"💥 Optimization pilot failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
