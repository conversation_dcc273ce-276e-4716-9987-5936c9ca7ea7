#!/usr/bin/env python3
"""
Optimized GraphRAG Configuration for Cost-Effective Legal Intelligence
Based on Phase 1 pilot test results and recommendations
"""

import logging
from dataclasses import dataclass
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

@dataclass
class OptimizedGraphRAGConfig:
    """Optimized configuration for cost-effective GraphRAG integration"""
    
    # Model Configuration - Cost Optimized
    model_name: str = "gemini-2.0-flash"
    max_tokens: int = 1000  # Reduced from 2000 to control costs
    temperature: float = 0.0
    cost_per_token: float = 0.000075  # Gemini 2.0 Flash pricing
    
    # Cost Controls - Stricter Limits
    max_cost_per_case: float = 0.005  # Reduced from $0.01 to $0.005
    daily_budget: float = 25.0  # Reduced from $50 to $25
    hourly_budget: float = 5.0  # Reduced from $10 to $5
    
    # Processing Optimization
    text_length_limit: int = 5000  # Reduced from 10000 to control token usage
    batch_size: int = 50  # Smaller batches for better cost control
    enable_entity_resolution: bool = False  # Disable to reduce processing costs
    
    # Selective Processing - Only High-Value Cases
    min_word_count: int = 200  # Only process cases with substantial content
    priority_courts: list = None  # Focus on specific courts if needed
    
    def __post_init__(self):
        if self.priority_courts is None:
            self.priority_courts = ['ca5', 'txsd', 'txed', 'txnd', 'txwd']  # Texas focus

class OptimizedLegalSchema:
    """Optimized legal schema focused on judge extraction efficiency"""
    
    @staticmethod
    def get_minimal_schema() -> Dict[str, Any]:
        """Get minimal schema focused on judge extraction only"""
        return {
            "node_types": [
                {
                    "label": "Judge",
                    "description": "Legal decision maker",
                    "properties": [
                        {"name": "name", "type": "STRING", "required": True},
                        {"name": "role", "type": "STRING"}
                    ]
                }
            ],
            "relationship_types": [
                {
                    "label": "PRESIDES_OVER",
                    "description": "Judge presiding over case"
                }
            ],
            "patterns": [
                ("Judge", "PRESIDES_OVER", "Case")
            ],
            "additional_node_types": False,
            "additional_relationship_types": False,
            "additional_patterns": False
        }
    
    @staticmethod
    def get_judge_focused_prompt() -> str:
        """Get optimized prompt focused specifically on judge extraction"""
        return """
        Extract ONLY judges and their roles from this legal document.
        
        Focus on:
        - Judge names (e.g., JONES, SMITH, RODRIGUEZ)
        - Their roles (e.g., Circuit Judge, District Judge, Chief Judge)
        - Court assignments
        
        Ignore:
        - Case parties (plaintiffs, defendants)
        - Legal concepts or doctrines
        - Procedural details
        - Citations or references
        
        Return minimal JSON with only judge information.
        """

class CostOptimizationStrategies:
    """Strategies for reducing GraphRAG costs while maintaining quality"""
    
    @staticmethod
    def should_process_with_graphrag(case_data: Dict[str, Any], config: OptimizedGraphRAGConfig) -> bool:
        """Determine if case should be processed with GraphRAG based on cost-benefit analysis"""
        
        # Skip if insufficient content
        text_content = case_data.get('plain_text', '')
        if len(text_content.split()) < config.min_word_count:
            return False
        
        # Skip if already has judge data
        if case_data.get('judge_name') or case_data.get('judge_metadata'):
            return False
        
        # Prioritize specific courts
        court_id = case_data.get('court_id', '')
        if config.priority_courts and court_id not in config.priority_courts:
            return False
        
        # Estimate cost and skip if too expensive
        estimated_tokens = len(text_content.split()) * 1.3
        estimated_cost = estimated_tokens * config.cost_per_token
        if estimated_cost > config.max_cost_per_case:
            return False
        
        return True
    
    @staticmethod
    def preprocess_text_for_efficiency(text: str, config: OptimizedGraphRAGConfig) -> str:
        """Preprocess text to reduce token usage while preserving judge information"""
        
        # Focus on sections likely to contain judge information
        judge_indicators = [
            'before', 'circuit judge', 'district judge', 'chief judge',
            'per curiam', 'concurring', 'dissenting', 'authored by',
            'presiding', 'panel', 'en banc'
        ]
        
        lines = text.split('\n')
        relevant_lines = []
        
        for line in lines:
            line_lower = line.lower()
            if any(indicator in line_lower for indicator in judge_indicators):
                relevant_lines.append(line)
            elif len(line.strip()) > 0 and line.strip().isupper() and len(line.strip().split()) <= 3:
                # Likely judge name in all caps
                relevant_lines.append(line)
        
        # If we found relevant lines, use them; otherwise use beginning of text
        if relevant_lines:
            processed_text = '\n'.join(relevant_lines)
        else:
            processed_text = text[:config.text_length_limit]
        
        # Ensure we don't exceed length limit
        if len(processed_text) > config.text_length_limit:
            processed_text = processed_text[:config.text_length_limit] + "..."
        
        return processed_text
    
    @staticmethod
    def get_cost_analysis(cases_processed: int, total_cost: float) -> Dict[str, Any]:
        """Analyze cost efficiency and provide recommendations"""
        
        avg_cost_per_case = total_cost / max(cases_processed, 1)
        
        # Project costs for full deployment
        projected_monthly_cost = avg_cost_per_case * 10000  # 10K cases/month
        projected_annual_cost = projected_monthly_cost * 12
        
        # Efficiency rating
        if avg_cost_per_case <= 0.005:
            efficiency_rating = "EXCELLENT"
        elif avg_cost_per_case <= 0.01:
            efficiency_rating = "GOOD"
        elif avg_cost_per_case <= 0.02:
            efficiency_rating = "ACCEPTABLE"
        else:
            efficiency_rating = "NEEDS_OPTIMIZATION"
        
        return {
            'avg_cost_per_case': avg_cost_per_case,
            'projected_monthly_cost': projected_monthly_cost,
            'projected_annual_cost': projected_annual_cost,
            'efficiency_rating': efficiency_rating,
            'cases_processed': cases_processed,
            'total_cost': total_cost,
            'recommendations': CostOptimizationStrategies._get_cost_recommendations(avg_cost_per_case)
        }
    
    @staticmethod
    def _get_cost_recommendations(avg_cost_per_case: float) -> list:
        """Get specific recommendations based on cost performance"""
        recommendations = []
        
        if avg_cost_per_case > 0.01:
            recommendations.extend([
                "Reduce max_tokens to 500-750",
                "Implement more aggressive text preprocessing",
                "Use selective processing for high-value cases only"
            ])
        
        if avg_cost_per_case > 0.02:
            recommendations.extend([
                "Consider alternative models (e.g., Gemini Flash 1.5)",
                "Implement batch processing with longer delays",
                "Use GraphRAG only for relationship discovery, not primary extraction"
            ])
        
        if avg_cost_per_case > 0.05:
            recommendations.extend([
                "Disable GraphRAG for primary extraction",
                "Use only for high-value relationship analysis",
                "Consider manual schema optimization"
            ])
        
        return recommendations

# Configuration instances for different deployment scenarios
DEVELOPMENT_CONFIG = OptimizedGraphRAGConfig(
    max_cost_per_case=0.01,
    daily_budget=10.0,
    hourly_budget=2.0,
    batch_size=10
)

STAGING_CONFIG = OptimizedGraphRAGConfig(
    max_cost_per_case=0.005,
    daily_budget=25.0,
    hourly_budget=5.0,
    batch_size=25
)

PRODUCTION_CONFIG = OptimizedGraphRAGConfig(
    max_cost_per_case=0.003,
    daily_budget=50.0,
    hourly_budget=10.0,
    batch_size=50,
    enable_entity_resolution=True
)
