#!/usr/bin/env python3
"""
Simple GraphRAG Test - Verify GraphRAG is working with one real case
"""

import asyncio
import logging
import os
import sys
import json
import time
from datetime import datetime
from dotenv import load_dotenv

# Add processing to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'courtlistener', 'processing', 'src'))

from processing.enhanced_judge_extractor import EnhancedJudgeExtractor, GraphRAGConfig
from processing.cost_monitor import CostLimits

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_single_case_graphrag():
    """Test GraphRAG with a single real legal document"""
    load_dotenv()
    
    logger.info("🧪 SIMPLE GRAPHRAG VERIFICATION TEST")
    logger.info("=" * 60)
    
    # Configure GraphRAG with conservative settings to avoid rate limits
    graphrag_config = GraphRAGConfig(
        model_name="gemini-2.0-flash",
        max_tokens=2000,
        temperature=0.0,
        cost_per_token=0.000075,
        max_cost_per_case=0.50,
        enable_entity_resolution=True,
        batch_size=1  # Process one at a time
    )
    
    # Cost monitoring
    cost_limits = CostLimits(
        daily_budget=50.0,
        hourly_budget=10.0,
        cost_per_case_limit=0.50,
        token_rate_limit=100000,
        request_rate_limit=100
    )
    
    enhanced_extractor = EnhancedJudgeExtractor(graphrag_config)
    enhanced_extractor.cost_monitor.limits = cost_limits
    
    # Create a test case with real legal content
    test_case = {
        'id': 'graphrag_verification_test',
        'case_name': 'Smith v. Johnson - GraphRAG Test',
        'court_id': 'ca5',
        'source': 'test',
        'plain_text': '''UNITED STATES COURT OF APPEALS
FOR THE FIFTH CIRCUIT

No. 24-40001

JOHN SMITH,
                                                  Plaintiff-Appellant,
v.
JOHNSON INDUSTRIES, INC.,
                                                  Defendant-Appellee.

Appeal from the United States District Court
for the Southern District of Texas

Before JONES, SMITH, and ELROD, Circuit Judges.

PER CURIAM:

This case involves a personal injury claim arising from a workplace accident. The district court granted summary judgment in favor of defendant Johnson Industries. We affirm.

JONES, Circuit Judge, concurring:

I agree with the majority's analysis but write separately to emphasize the importance of workplace safety regulations in industrial settings. The evidence clearly shows that plaintiff failed to follow established safety protocols.

The record demonstrates that Johnson Industries provided adequate training and safety equipment. Plaintiff's failure to utilize the provided safety harness directly contributed to the accident.

ELROD, Circuit Judge, dissenting:

I respectfully dissent from the majority's holding. The record contains genuine issues of material fact regarding the adequacy of defendant's safety training program. 

The evidence shows that the safety training was conducted in English only, despite the fact that plaintiff's primary language is Spanish. This raises questions about whether plaintiff truly understood the safety requirements.

Furthermore, the safety equipment provided was not properly maintained. The inspection records show that the safety harness had not been inspected for over six months prior to the accident.

The judgment of the district court is AFFIRMED.''',
        'word_count': 250
    }
    
    try:
        logger.info("Testing GraphRAG with single legal document...")
        logger.info(f"Document length: {len(test_case['plain_text'])} characters")
        logger.info(f"Expected judges: JONES, SMITH, ELROD")
        
        start_time = time.time()
        
        # Run enhanced extraction
        judges, metrics = await enhanced_extractor.extract_judges_enhanced(test_case)
        
        processing_time = time.time() - start_time
        total_cost = sum(m.token_cost for m in metrics.values())
        
        logger.info("")
        logger.info("🎯 RESULTS:")
        logger.info(f"Processing time: {processing_time:.2f} seconds")
        logger.info(f"Total cost: ${total_cost:.4f}")
        logger.info(f"Judges found: {len(judges)}")
        
        if judges:
            logger.info("✅ JUDGES EXTRACTED:")
            for judge in judges:
                logger.info(f"  - {judge.name} (confidence: {judge.confidence:.2f}, method: {judge.extraction_method})")
        else:
            logger.info("❌ No judges found")
        
        logger.info("")
        logger.info("📊 METHOD BREAKDOWN:")
        for method, metric in metrics.items():
            logger.info(f"  {method.upper()}:")
            logger.info(f"    Judges found: {metric.judges_found}")
            logger.info(f"    Confidence: {metric.confidence_avg:.2f}")
            logger.info(f"    Cost: ${metric.token_cost:.4f}")
            logger.info(f"    Time: {metric.processing_time:.2f}s")
        
        # Check if GraphRAG worked
        graphrag_worked = any('graphrag' in judge.extraction_method.lower() for judge in judges)
        pattern_worked = any('pattern' in judge.extraction_method.lower() or 'text' in judge.extraction_method.lower() for judge in judges)
        
        logger.info("")
        logger.info("🔍 ANALYSIS:")
        logger.info(f"GraphRAG extraction: {'✅ SUCCESS' if graphrag_worked else '❌ FAILED'}")
        logger.info(f"Pattern extraction: {'✅ SUCCESS' if pattern_worked else '❌ FAILED'}")
        
        if graphrag_worked:
            logger.info("🎉 GraphRAG is working correctly with real legal documents!")
        elif pattern_worked:
            logger.info("⚠️ GraphRAG failed but pattern extraction worked (fallback successful)")
        else:
            logger.info("❌ Both GraphRAG and pattern extraction failed")
        
        # Save results
        results = {
            'test_id': 'simple_graphrag_verification',
            'timestamp': datetime.now().isoformat(),
            'processing_time': processing_time,
            'total_cost': total_cost,
            'judges_found': len(judges),
            'judges': [{'name': j.name, 'confidence': j.confidence, 'method': j.extraction_method} for j in judges],
            'metrics': {k: {
                'method': v.method_name,
                'judges_found': v.judges_found,
                'confidence_avg': v.confidence_avg,
                'token_cost': v.token_cost,
                'processing_time': v.processing_time
            } for k, v in metrics.items()},
            'graphrag_success': graphrag_worked,
            'pattern_success': pattern_worked
        }
        
        with open('simple_graphrag_test_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info("Results saved to simple_graphrag_test_results.json")
        
        return graphrag_worked
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test execution"""
    success = await test_single_case_graphrag()
    
    if success:
        logger.info("")
        logger.info("🎉 GRAPHRAG VERIFICATION: SUCCESS")
        logger.info("GraphRAG is working with real legal documents!")
    else:
        logger.info("")
        logger.info("❌ GRAPHRAG VERIFICATION: NEEDS INVESTIGATION")
        logger.info("Check logs above for specific issues")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
