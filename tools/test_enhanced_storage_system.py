#!/usr/bin/env python3
"""
Enhanced Storage System Test Suite
Comprehensive testing for the GraphRAG storage orchestration system.

Tests:
1. Global UID tracking across all backends
2. Schema storage and caching
3. ETL checkpoints and recovery
4. Data integrity validation
5. Idempotent operations
6. Error handling and rollback
7. GraphRAG integration
"""

import sys
import os
import json
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import uuid

# Add the processing module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'courtlistener', 'processing', 'src'))

try:
    from processing.storage.enhanced_storage_orchestrator import (
        EnhancedStorageOrchestrator, 
        GlobalUID, 
        StorageOperationType,
        StorageBackend,
        ETLCheckpoint
    )
    from processing.storage.graphrag_storage_manager import (
        GraphRAGStorageManager,
        PracticeArea,
        LegalSchema,
        GraphRAGResult
    )
    from processing.storage.enhanced_neo4j_connector import EnhancedNeo4jConnector
    from processing.storage.supabase_connector import SupabaseConnector
    from processing.storage.pinecone_connector import PineconeConnector
    from processing.storage import gcs_helper
except ImportError as e:
    print(f"Failed to import storage modules: {e}")
    print("Please ensure the courtlistener processing modules are properly set up")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedStorageSystemTests:
    """Comprehensive test suite for enhanced storage system"""
    
    def __init__(self):
        """Initialize test suite"""
        self.results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'errors': [],
            'detailed_results': {}
        }
        
        # Initialize components
        self.orchestrator = None
        self.graphrag_manager = None
        self.neo4j_connector = None
        
        print("Enhanced Storage System Test Suite Initialized")
    
    def setup_components(self):
        """Set up storage components for testing"""
        try:
            # Initialize enhanced Neo4j connector
            self.neo4j_connector = EnhancedNeo4jConnector(enable_graphrag=True)
            
            # Initialize storage orchestrator
            self.orchestrator = EnhancedStorageOrchestrator(
                neo4j_connector=self.neo4j_connector
            )
            
            # Initialize GraphRAG storage manager
            self.graphrag_manager = GraphRAGStorageManager(self.orchestrator)
            
            print("✅ Storage components initialized successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize storage components: {e}")
            return False
    
    def run_test(self, test_name: str, test_func, *args, **kwargs):
        """Run a single test and record results"""
        self.results['total_tests'] += 1
        
        try:
            print(f"\n🧪 Running test: {test_name}")
            result = test_func(*args, **kwargs)
            
            if result:
                self.results['passed'] += 1
                self.results['detailed_results'][test_name] = {'status': 'PASSED', 'details': result}
                print(f"✅ {test_name} PASSED")
            else:
                self.results['failed'] += 1
                self.results['detailed_results'][test_name] = {'status': 'FAILED', 'details': 'Test returned False'}
                print(f"❌ {test_name} FAILED")
            
        except Exception as e:
            self.results['failed'] += 1
            error_msg = f"Test error: {str(e)}"
            self.results['errors'].append(f"{test_name}: {error_msg}")
            self.results['detailed_results'][test_name] = {'status': 'ERROR', 'details': error_msg}
            print(f"💥 {test_name} ERROR: {e}")
    
    async def run_async_test(self, test_name: str, test_func, *args, **kwargs):
        """Run an async test and record results"""
        self.results['total_tests'] += 1
        
        try:
            print(f"\n🧪 Running async test: {test_name}")
            result = await test_func(*args, **kwargs)
            
            if result:
                self.results['passed'] += 1
                self.results['detailed_results'][test_name] = {'status': 'PASSED', 'details': result}
                print(f"✅ {test_name} PASSED")
            else:
                self.results['failed'] += 1
                self.results['detailed_results'][test_name] = {'status': 'FAILED', 'details': 'Test returned False'}
                print(f"❌ {test_name} FAILED")
            
        except Exception as e:
            self.results['failed'] += 1
            error_msg = f"Test error: {str(e)}"
            self.results['errors'].append(f"{test_name}: {error_msg}")
            self.results['detailed_results'][test_name] = {'status': 'ERROR', 'details': error_msg}
            print(f"💥 {test_name} ERROR: {e}")
    
    # === Test Cases ===
    
    def test_connection_status(self):
        """Test connections to all storage backends"""
        try:
            # Test Neo4j connection
            neo4j_status = self.neo4j_connector.test_connection()
            print(f"Neo4j status: {neo4j_status['status']}")
            
            # Test Supabase connection (simple query)
            supabase_test = self.orchestrator.supabase.select_records('global_uid_registry', limit=1)
            print(f"Supabase status: {'connected' if isinstance(supabase_test, list) else 'error'}")
            
            # Test Pinecone connection
            pinecone_stats = self.orchestrator.pinecone.get_jurisdiction_stats('tx')
            print(f"Pinecone status: {'connected' if isinstance(pinecone_stats, dict) else 'error'}")
            
            # Test GCS (basic function availability)
            gcs_available = hasattr(gcs_helper, 'store_case_document')
            print(f"GCS helper status: {'available' if gcs_available else 'unavailable'}")
            
            return neo4j_status['status'] == 'connected'
            
        except Exception as e:
            print(f"Connection test error: {e}")
            return False
    
    def test_global_uid_generation(self):
        """Test global UID generation and registration"""
        try:
            # Generate test global UID
            global_uid = self.orchestrator.generate_global_uid(
                entity_type="test_entity",
                source_system="test_system",
                source_id="test_123",
                metadata={"test": True}
            )
            
            print(f"Generated global UID: {global_uid.uid}")
            
            # Verify UID is registered
            retrieved_uid = self.orchestrator.get_global_uid(global_uid.uid)
            
            if retrieved_uid:
                print(f"UID successfully registered and retrieved")
                return True
            else:
                print(f"UID registration failed")
                return False
                
        except Exception as e:
            print(f"Global UID test error: {e}")
            return False
    
    def test_schema_storage_and_caching(self):
        """Test GraphRAG schema storage and caching"""
        try:
            # Create test schema
            test_schema = {
                "node_types": [
                    {"label": "TestEntity", "properties": [{"name": "test_prop", "type": "STRING"}]}
                ],
                "relationship_types": [
                    {"label": "TEST_RELATION", "properties": []}
                ],
                "metadata": {"test": True}
            }
            
            # Store schema
            schema_id = self.orchestrator.store_graphrag_schema(
                practice_area="test_area",
                schema_data=test_schema,
                schema_version="1.0"
            )
            
            print(f"Stored schema with ID: {schema_id}")
            
            # Retrieve cached schema
            cached_schema = self.orchestrator.get_cached_schema("test_area")
            
            if cached_schema and cached_schema.get('node_types'):
                print("Schema caching successful")
                return True
            else:
                print("Schema caching failed")
                return False
                
        except Exception as e:
            print(f"Schema caching test error: {e}")
            return False
    
    def test_etl_checkpoint_management(self):
        """Test ETL checkpoint creation and management"""
        try:
            # Create test checkpoint
            checkpoint = self.orchestrator.create_etl_checkpoint(
                pipeline_name="test_pipeline",
                batch_id="test_batch_001",
                metadata={"test": True}
            )
            
            print(f"Created checkpoint: {checkpoint.checkpoint_id}")
            
            # Update checkpoint with processed UIDs
            test_uids = ["uid_1", "uid_2", "uid_3"]
            failed_uids = ["uid_4"]
            
            update_success = self.orchestrator.update_etl_checkpoint(
                checkpoint.checkpoint_id,
                processed_uids=test_uids,
                failed_uids=failed_uids
            )
            
            if update_success:
                print("Checkpoint update successful")
                
                # Test resume from checkpoint
                processed, failed = self.orchestrator.resume_from_checkpoint(checkpoint.checkpoint_id)
                
                if len(processed) == 3 and len(failed) == 1:
                    print("Checkpoint resume successful")
                    return True
                else:
                    print(f"Checkpoint resume failed: {len(processed)} processed, {len(failed)} failed")
                    return False
            else:
                print("Checkpoint update failed")
                return False
                
        except Exception as e:
            print(f"ETL checkpoint test error: {e}")
            return False
    
    async def test_graphrag_entity_storage(self):
        """Test GraphRAG entity storage across all backends"""
        try:
            # Generate test entity
            global_uid = self.orchestrator.generate_global_uid(
                entity_type="graphrag_entity",
                source_system="test_graphrag",
                source_id="entity_001"
            )
            
            test_entity = {
                'type': 'Judge',
                'name': 'Test Judge',
                'properties': {
                    'court': 'Test Court',
                    'jurisdiction': 'TX',
                    'appointed_year': 2020
                },
                'embeddings': [0.1] * 1024  # Mock embedding vector
            }
            
            # Store entity across all backends
            storage_results = await self.orchestrator.store_graphrag_entity(
                global_uid=global_uid,
                entity_data=test_entity,
                jurisdiction='tx',
                practice_area='test_area'
            )
            
            print(f"Storage results: {storage_results}")
            
            # Check if stored in all backends
            success_count = sum(1 for success in storage_results.values() if success)
            total_backends = len(storage_results)
            
            print(f"Stored successfully in {success_count}/{total_backends} backends")
            
            return success_count >= 3  # At least 3 backends should succeed
            
        except Exception as e:
            print(f"GraphRAG entity storage test error: {e}")
            return False
    
    async def test_data_integrity_validation(self):
        """Test data integrity validation across backends"""
        try:
            # Create test entity first
            global_uid = self.orchestrator.generate_global_uid(
                entity_type="integrity_test",
                source_system="test_system",
                source_id="integrity_001"
            )
            
            test_entity = {
                'type': 'TestEntity',
                'name': 'Integrity Test Entity',
                'properties': {'test': True}
            }
            
            # Store entity
            await self.orchestrator.store_graphrag_entity(
                global_uid=global_uid,
                entity_data=test_entity,
                jurisdiction='tx',
                practice_area='test_area'
            )
            
            # Wait a moment for propagation
            await asyncio.sleep(1)
            
            # Validate integrity
            validation_report = await self.orchestrator.validate_global_uid_integrity(global_uid.uid)
            
            print(f"Integrity validation: {validation_report['consistency_score']:.2f}")
            print(f"Backend status: {validation_report['backend_status']}")
            
            # Check if at least some backends have the data
            backend_count = sum(1 for status in validation_report['backend_status'].values() if status)
            
            return backend_count >= 2 and validation_report['consistency_score'] > 0.5
            
        except Exception as e:
            print(f"Data integrity validation test error: {e}")
            return False
    
    def test_practice_area_schema_management(self):
        """Test practice area-specific schema management"""
        try:
            # Test schema prompt generation for different practice areas
            practice_areas = [PracticeArea.PERSONAL_INJURY, PracticeArea.CRIMINAL_DEFENSE, PracticeArea.FAMILY_LAW]
            
            schema_results = {}
            
            for practice_area in practice_areas:
                prompt = self.graphrag_manager.get_legal_schema_prompt(practice_area)
                schema_results[practice_area.value] = {
                    'has_prompt': bool(prompt and len(prompt) > 100),
                    'contains_practice_specific': practice_area.value.replace('_', ' ').lower() in prompt.lower()
                }
                
                print(f"{practice_area.value}: prompt length {len(prompt)}, practice-specific: {schema_results[practice_area.value]['contains_practice_specific']}")
            
            # Check if all practice areas have valid prompts
            all_valid = all(
                result['has_prompt'] and result['contains_practice_specific'] 
                for result in schema_results.values()
            )
            
            return all_valid
            
        except Exception as e:
            print(f"Practice area schema test error: {e}")
            return False
    
    def test_idempotent_operations(self):
        """Test idempotent operations in Neo4j connector"""
        try:
            # Test idempotent entity creation
            test_uid = str(uuid.uuid4())
            
            entity_data = {
                'type': 'IdempotentTest',
                'name': 'Test Entity',
                'properties': {'test': True}
            }
            
            # Create entity twice - should be idempotent
            result1 = self.neo4j_connector.create_graphrag_entity(
                global_uid=test_uid,
                entity_data=entity_data,
                practice_area='test',
                jurisdiction='tx'
            )
            
            result2 = self.neo4j_connector.create_graphrag_entity(
                global_uid=test_uid,
                entity_data=entity_data,
                practice_area='test',
                jurisdiction='tx'
            )
            
            # Both operations should succeed
            if result1 and result2:
                print("Idempotent entity creation successful")
                
                # Verify only one entity exists
                entity = self.neo4j_connector.get_graphrag_entity(test_uid)
                
                if entity and entity.get('global_uid') == test_uid:
                    print("Entity uniqueness verified")
                    return True
                else:
                    print("Entity verification failed")
                    return False
            else:
                print("Idempotent operation failed")
                return False
                
        except Exception as e:
            print(f"Idempotent operations test error: {e}")
            return False
    
    def test_batch_operations(self):
        """Test batch operations for entities and relationships"""
        try:
            # Create test entities in batch
            test_entities = []
            entity_uids = []
            
            for i in range(5):
                uid = str(uuid.uuid4())
                entity_uids.append(uid)
                test_entities.append({
                    'global_uid': uid,
                    'data': {
                        'type': 'BatchTest',
                        'name': f'Batch Entity {i}',
                        'properties': {'batch_id': i}
                    }
                })
            
            # Execute batch creation
            batch_results = self.neo4j_connector.create_graphrag_entities_batch(
                entities=test_entities,
                practice_area='test_batch',
                jurisdiction='tx'
            )
            
            print(f"Batch results: {batch_results['created']} created, {batch_results['failed']} failed")
            
            # Test batch relationships
            test_relationships = []
            for i in range(len(entity_uids) - 1):
                test_relationships.append({
                    'global_uid': str(uuid.uuid4()),
                    'source_uid': entity_uids[i],
                    'target_uid': entity_uids[i + 1],
                    'data': {
                        'type': 'BATCH_RELATED',
                        'properties': {'batch_order': i}
                    }
                })
            
            rel_results = self.neo4j_connector.create_graphrag_relationships_batch(test_relationships)
            
            print(f"Relationship batch results: {rel_results['created']} created, {rel_results['failed']} failed")
            
            return (batch_results['created'] >= 4 and batch_results['failed'] <= 1 and 
                   rel_results['created'] >= 3 and rel_results['failed'] <= 1)
            
        except Exception as e:
            print(f"Batch operations test error: {e}")
            return False
    
    def test_storage_statistics(self):
        """Test storage statistics and monitoring"""
        try:
            # Get orchestrator statistics
            orch_stats = self.orchestrator.get_storage_statistics()
            print(f"Orchestrator stats: {json.dumps(orch_stats, indent=2, default=str)}")
            
            # Get Neo4j GraphRAG statistics
            neo4j_stats = self.neo4j_connector.get_graphrag_statistics()
            print(f"Neo4j GraphRAG stats: {json.dumps(neo4j_stats, indent=2, default=str)}")
            
            # Get GraphRAG manager statistics
            graphrag_stats = self.graphrag_manager.get_practice_area_stats()
            print(f"GraphRAG manager stats: {json.dumps(graphrag_stats, indent=2, default=str)}")
            
            # Verify statistics are valid
            stats_valid = (
                isinstance(orch_stats, dict) and 'timestamp' in orch_stats and
                isinstance(neo4j_stats, dict) and 'timestamp' in neo4j_stats and
                isinstance(graphrag_stats, dict)
            )
            
            return stats_valid
            
        except Exception as e:
            print(f"Storage statistics test error: {e}")
            return False
    
    def test_error_handling_and_recovery(self):
        """Test error handling and recovery mechanisms"""
        try:
            # Test handling of invalid data
            invalid_uid = "invalid-uid-format"
            
            # This should handle the error gracefully
            result = self.orchestrator.get_global_uid(invalid_uid)
            
            # Should return None for invalid UID
            if result is None:
                print("Invalid UID handled correctly")
                
                # Test recovery from failed operations
                try:
                    # Attempt operation that might fail
                    fake_entity = {'invalid': 'structure'}
                    
                    # This should not crash the system
                    recovery_result = self.neo4j_connector.create_graphrag_entity(
                        global_uid=str(uuid.uuid4()),
                        entity_data=fake_entity,
                        practice_area='test',
                        jurisdiction='tx'
                    )
                    
                    print(f"Error recovery test completed: {recovery_result}")
                    return True
                    
                except Exception as recovery_error:
                    print(f"Error handled gracefully: {recovery_error}")
                    return True  # Error was handled, which is good
            
            return False
            
        except Exception as e:
            print(f"Error handling test error: {e}")
            return True  # If we get here, error handling worked
    
    # === Main Test Execution ===
    
    async def run_all_tests(self):
        """Run the complete test suite"""
        print("\n" + "="*80)
        print("🚀 ENHANCED STORAGE SYSTEM TEST SUITE")
        print("="*80)
        
        # Setup
        if not self.setup_components():
            print("❌ Failed to set up components. Aborting tests.")
            return False
        
        # Run synchronous tests
        sync_tests = [
            ("Connection Status", self.test_connection_status),
            ("Global UID Generation", self.test_global_uid_generation),
            ("Schema Storage and Caching", self.test_schema_storage_and_caching),
            ("ETL Checkpoint Management", self.test_etl_checkpoint_management),
            ("Practice Area Schema Management", self.test_practice_area_schema_management),
            ("Idempotent Operations", self.test_idempotent_operations),
            ("Batch Operations", self.test_batch_operations),
            ("Storage Statistics", self.test_storage_statistics),
            ("Error Handling and Recovery", self.test_error_handling_and_recovery)
        ]
        
        for test_name, test_func in sync_tests:
            self.run_test(test_name, test_func)
        
        # Run asynchronous tests
        async_tests = [
            ("GraphRAG Entity Storage", self.test_graphrag_entity_storage),
            ("Data Integrity Validation", self.test_data_integrity_validation)
        ]
        
        for test_name, test_func in async_tests:
            await self.run_async_test(test_name, test_func)
        
        # Print final results
        self.print_final_results()
        
        return self.results['failed'] == 0
    
    def print_final_results(self):
        """Print comprehensive test results"""
        print("\n" + "="*80)
        print("📊 TEST RESULTS SUMMARY")
        print("="*80)
        
        print(f"Total Tests: {self.results['total_tests']}")
        print(f"✅ Passed: {self.results['passed']}")
        print(f"❌ Failed: {self.results['failed']}")
        
        success_rate = (self.results['passed'] / self.results['total_tests']) * 100 if self.results['total_tests'] > 0 else 0
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if self.results['errors']:
            print(f"\n🔍 ERRORS:")
            for error in self.results['errors']:
                print(f"   • {error}")
        
        print(f"\n🔍 DETAILED RESULTS:")
        for test_name, result in self.results['detailed_results'].items():
            status_emoji = "✅" if result['status'] == 'PASSED' else "❌" if result['status'] == 'FAILED' else "💥"
            print(f"   {status_emoji} {test_name}: {result['status']}")
        
        # Overall assessment
        if self.results['failed'] == 0:
            print(f"\n🎉 ALL TESTS PASSED! Enhanced storage system is working correctly.")
        elif self.results['passed'] > self.results['failed']:
            print(f"\n⚠️  MOSTLY SUCCESSFUL: {self.results['passed']}/{self.results['total_tests']} tests passed.")
        else:
            print(f"\n🚨 SIGNIFICANT ISSUES: Only {self.results['passed']}/{self.results['total_tests']} tests passed.")
    
    def cleanup(self):
        """Clean up test resources"""
        try:
            if self.graphrag_manager:
                self.graphrag_manager.close()
            
            if self.orchestrator:
                self.orchestrator.close()
            
            print("🧹 Test cleanup completed")
            
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")

# === Main Execution ===

async def main():
    """Main test execution function"""
    test_suite = EnhancedStorageSystemTests()
    
    try:
        success = await test_suite.run_all_tests()
        return success
    
    finally:
        test_suite.cleanup()

if __name__ == "__main__":
    # Run the test suite
    try:
        success = asyncio.run(main())
        exit_code = 0 if success else 1
        sys.exit(exit_code)
    
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        sys.exit(1)