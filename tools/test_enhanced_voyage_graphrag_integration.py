#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Voyage-context-3 GraphRAG Integration

Tests the complete pipeline including:
- 2k token document chunking with legal optimization
- Voyage-context-3 embedding generation
- GraphRAG entity and relationship extraction
- Practice-area specific Pinecone storage
- Hybrid search capabilities
- Cost optimization and performance monitoring
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# Add the processing module to path
sys.path.append(str(Path(__file__).parent / "courtlistener" / "processing" / "src"))

from processing.enhanced_voyage_graphrag_processor import EnhancedVoyageGraphRAGProcessor, EmbeddingType
from processing.storage.pinecone_connector import PineconeConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_voyage_graphrag_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedVoyageGraphRAGTester:
    """Comprehensive tester for the enhanced GraphRAG integration"""
    
    def __init__(self):
        """Initialize the tester"""
        self.processor = None
        self.test_results = {
            'chunking_tests': {},
            'embedding_tests': {},
            'graphrag_tests': {},
            'storage_tests': {},
            'hybrid_search_tests': {},
            'performance_tests': {},
            'cost_analysis': {}
        }
        
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run the complete test suite"""
        logger.info("🚀 Starting Enhanced Voyage-context-3 GraphRAG Integration Tests")
        logger.info("=" * 80)
        
        try:
            # Initialize processor
            await self._initialize_processor()
            
            # Test 1: Document chunking with legal optimization
            await self._test_legal_document_chunking()
            
            # Test 2: Voyage-context-3 embedding generation
            await self._test_voyage_embedding_generation()
            
            # Test 3: GraphRAG integration
            await self._test_graphrag_integration()
            
            # Test 4: Practice-area specific storage
            await self._test_practice_area_storage()
            
            # Test 5: Hybrid search capabilities
            await self._test_hybrid_search()
            
            # Test 6: Performance and cost analysis
            await self._test_performance_analysis()
            
            # Generate final report
            return await self._generate_final_report()
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            return {'error': str(e)}
        
        finally:
            if self.processor:
                self.processor.close()
    
    async def _initialize_processor(self):
        """Initialize the enhanced processor with test configuration"""
        logger.info("🔧 Initializing Enhanced Voyage-context-3 GraphRAG Processor...")
        
        # Test configuration with conservative limits
        test_config = {
            'cost_limits': {
                'max_cost_per_document': 0.05,
                'daily_budget': 10.0,
                'hourly_budget': 2.0
            },
            'batch_size': 10,
            'max_concurrent': 3,
            'chunking': {
                'chunk_size': 1500,  # Slightly smaller for testing
                'overlap_size': 150,
                'preserve_sentences': True
            }
        }
        
        self.processor = EnhancedVoyageGraphRAGProcessor(test_config)
        logger.info("✅ Processor initialized successfully")
    
    async def _test_legal_document_chunking(self):
        """Test 2k token chunking with legal optimization"""
        logger.info("\n📄 Testing Legal Document Chunking...")
        
        # Test document with various legal elements
        test_document = """
        UNITED STATES COURT OF APPEALS FOR THE FIFTH CIRCUIT
        
        No. 21-40123
        
        JOHN SMITH, Plaintiff-Appellant,
        v.
        ABC MEDICAL CENTER, Defendant-Appellee.
        
        Appeal from the United States District Court for the Western District of Texas
        
        Before JONES, MARTINEZ, and RODRIGUEZ, Circuit Judges.
        
        PER CURIAM:
        
        I. FACTUAL BACKGROUND
        
        This medical malpractice case arises from the treatment of John Smith at ABC Medical Center. 
        Plaintiff alleges that defendant's negligent care resulted in permanent injury. The district 
        court granted summary judgment in favor of defendant, finding no genuine issue of material fact.
        
        II. PROCEDURAL HISTORY
        
        Smith filed his complaint in state court on January 15, 2020, alleging medical negligence 
        under Texas Civil Practice and Remedies Code § 74.001. Defendant removed the case to federal 
        court based on diversity jurisdiction pursuant to 28 U.S.C. § 1332.
        
        III. STANDARD OF REVIEW
        
        We review a district court's grant of summary judgment de novo. Anderson v. Liberty Lobby, 
        Inc., 477 U.S. 242, 248 (1986). Summary judgment is appropriate when there is no genuine 
        issue as to any material fact and the moving party is entitled to judgment as a matter of law.
        
        IV. ANALYSIS
        
        A. Medical Standard of Care
        
        Under Texas law, a plaintiff in a medical malpractice case must prove: (1) the degree of 
        skill and care ordinarily exercised by physicians in the same or similar circumstances, and 
        (2) that the defendant departed from that standard. Tex. Civ. Prac. & Rem. Code § 74.101.
        
        B. Expert Testimony Requirements
        
        Texas requires expert testimony to establish the standard of care in medical malpractice cases. 
        The expert must be qualified under the detailed requirements of § 74.401. Here, plaintiff's 
        expert failed to satisfy these statutory requirements.
        
        V. CONCLUSION
        
        For the foregoing reasons, we AFFIRM the district court's grant of summary judgment in favor 
        of defendant ABC Medical Center.
        
        JONES, Circuit Judge, concurring:
        
        I concur in the judgment but write separately to address the broader implications of expert 
        testimony requirements in medical malpractice cases.
        
        MARTINEZ, Circuit Judge, dissenting:
        
        I respectfully dissent. The majority's analysis of the expert testimony requirements is 
        overly restrictive and inconsistent with Texas precedent. I would reverse and remand for trial.
        """
        
        try:
            start_time = time.time()
            
            # Test chunking
            chunks = self.processor.chunk_legal_document(
                text=test_document,
                metadata={'case_type': 'medical_malpractice', 'jurisdiction': 'tx'}
            )
            
            chunking_time = time.time() - start_time
            
            # Analyze results
            self.test_results['chunking_tests'] = {
                'total_chunks': len(chunks),
                'avg_chunk_size': sum(chunk['token_count'] for chunk in chunks) / len(chunks) if chunks else 0,
                'legal_indicators_detected': sum(1 for chunk in chunks if chunk['legal_indicators']['has_citations']),
                'chunking_time_seconds': chunking_time,
                'chunks_with_legal_content': sum(1 for chunk in chunks if chunk['legal_indicators']['legal_density'] > 0.1),
                'success': len(chunks) > 0
            }
            
            logger.info(f"✅ Chunking test complete: {len(chunks)} chunks generated")
            logger.info(f"   Avg chunk size: {self.test_results['chunking_tests']['avg_chunk_size']:.0f} tokens")
            logger.info(f"   Legal content chunks: {self.test_results['chunking_tests']['chunks_with_legal_content']}")
            
            # Store chunks for next tests
            self.test_chunks = chunks
            
        except Exception as e:
            logger.error(f"❌ Chunking test failed: {e}")
            self.test_results['chunking_tests'] = {'success': False, 'error': str(e)}
    
    async def _test_voyage_embedding_generation(self):
        """Test Voyage-context-3 embedding generation"""
        logger.info("\n🚀 Testing Voyage-context-3 Embedding Generation...")
        
        if not hasattr(self, 'test_chunks') or not self.test_chunks:
            logger.error("❌ No chunks available for embedding test")
            self.test_results['embedding_tests'] = {'success': False, 'error': 'No chunks available'}
            return
        
        try:
            start_time = time.time()
            
            # Test embedding generation for chunks
            chunk_texts = [chunk['text'] for chunk in self.test_chunks[:3]]  # Test first 3 chunks
            chunk_embeddings = await self.processor.generate_embeddings(
                texts=chunk_texts,
                embedding_type=EmbeddingType.DOCUMENT_CHUNKS
            )
            
            # Test entity embedding generation
            test_entities = ["Judge Jones", "ABC Medical Center", "Texas Civil Practice Code"]
            entity_embeddings = await self.processor.generate_embeddings(
                texts=test_entities,
                embedding_type=EmbeddingType.ENTITIES
            )
            
            embedding_time = time.time() - start_time
            
            # Analyze results
            self.test_results['embedding_tests'] = {
                'chunk_embeddings_generated': len(chunk_embeddings) if chunk_embeddings else 0,
                'entity_embeddings_generated': len(entity_embeddings) if entity_embeddings else 0,
                'embedding_dimension': len(chunk_embeddings[0]) if chunk_embeddings else 0,
                'embedding_time_seconds': embedding_time,
                'avg_time_per_embedding': embedding_time / max(len(chunk_embeddings) + len(entity_embeddings), 1),
                'success': bool(chunk_embeddings and entity_embeddings)
            }
            
            logger.info(f"✅ Embedding test complete:")
            logger.info(f"   Chunk embeddings: {len(chunk_embeddings) if chunk_embeddings else 0}")
            logger.info(f"   Entity embeddings: {len(entity_embeddings) if entity_embeddings else 0}")
            logger.info(f"   Dimension: {len(chunk_embeddings[0]) if chunk_embeddings else 0}")
            
            # Store embeddings for next tests
            self.test_embeddings = {
                'chunks': chunk_embeddings,
                'entities': entity_embeddings
            }
            
        except Exception as e:
            logger.error(f"❌ Embedding test failed: {e}")
            self.test_results['embedding_tests'] = {'success': False, 'error': str(e)}
    
    async def _test_graphrag_integration(self):
        """Test GraphRAG integration with embeddings"""
        logger.info("\n🔗 Testing GraphRAG Integration...")
        
        # Test document for GraphRAG processing
        test_case_data = {
            'cl_opinion_id': 'test_12345',
            'case_name': 'Smith v. ABC Medical Center',
            'jurisdiction': 'tx',
            'court': 'United States Court of Appeals for the Fifth Circuit',
            'practice_area': 'personal_injury',
            'date_filed': '2020-01-15'
        }
        
        test_document = """
        This medical malpractice case involves plaintiff John Smith and defendant ABC Medical Center. 
        Judge Martinez presided over the case in the Western District Court of Texas. The case was 
        decided under Texas Civil Practice and Remedies Code Section 74.001. Expert witness testimony 
        was provided by Dr. Johnson regarding the standard of care. The court found that plaintiff 
        failed to establish a breach of the medical standard of care.
        """
        
        try:
            start_time = time.time()
            
            # Process document through complete GraphRAG pipeline
            result = await self.processor.process_graphrag_document(
                document_text=test_document,
                case_metadata=test_case_data,
                practice_area="personal_injury"
            )
            
            processing_time = time.time() - start_time
            
            if result:
                self.test_results['graphrag_tests'] = {
                    'document_processed': True,
                    'global_uid_generated': result.get('global_uid') is not None,
                    'chunks_generated': result['processing_stats']['chunks_generated'],
                    'entities_extracted': result['processing_stats']['entities_extracted'],
                    'relationships_extracted': result['processing_stats']['relationships_extracted'],
                    'embeddings_generated': result['processing_stats']['embeddings_generated'],
                    'processing_time_seconds': processing_time,
                    'storage_success': any(result['storage_results'].values()),
                    'success': True
                }
                
                logger.info(f"✅ GraphRAG integration test complete:")
                logger.info(f"   Global UID: {result.get('global_uid', 'N/A')}")
                logger.info(f"   Chunks: {result['processing_stats']['chunks_generated']}")
                logger.info(f"   Entities: {result['processing_stats']['entities_extracted']}")
                logger.info(f"   Relationships: {result['processing_stats']['relationships_extracted']}")
                
                # Store result for next tests
                self.test_graphrag_result = result
                
            else:
                self.test_results['graphrag_tests'] = {'success': False, 'error': 'No result returned'}
                
        except Exception as e:
            logger.error(f"❌ GraphRAG integration test failed: {e}")
            self.test_results['graphrag_tests'] = {'success': False, 'error': str(e)}
    
    async def _test_practice_area_storage(self):
        """Test practice-area specific storage in Pinecone"""
        logger.info("\n💾 Testing Practice-Area Specific Storage...")
        
        try:
            # Test Pinecone connector directly
            pinecone = PineconeConnector()
            
            # Test namespace generation
            namespace_chunks = pinecone.get_graphrag_namespace("tx", "personal_injury", "document_chunks")
            namespace_entities = pinecone.get_graphrag_namespace("tx", "personal_injury", "entities")
            namespace_relationships = pinecone.get_graphrag_namespace("tx", "personal_injury", "relationships")
            
            # Test embedding statistics
            stats = pinecone.get_embedding_statistics("tx", "personal_injury")
            
            self.test_results['storage_tests'] = {
                'namespace_chunks': namespace_chunks,
                'namespace_entities': namespace_entities,
                'namespace_relationships': namespace_relationships,
                'embedding_stats': stats,
                'namespaces_generated': all([namespace_chunks, namespace_entities, namespace_relationships]),
                'success': True
            }
            
            logger.info(f"✅ Storage test complete:")
            logger.info(f"   Chunks namespace: {namespace_chunks}")
            logger.info(f"   Entities namespace: {namespace_entities}")
            logger.info(f"   Total embeddings: {stats.get('total_embeddings', 0)}")
            
            pinecone.close()
            
        except Exception as e:
            logger.error(f"❌ Storage test failed: {e}")
            self.test_results['storage_tests'] = {'success': False, 'error': str(e)}
    
    async def _test_hybrid_search(self):
        """Test hybrid search capabilities"""
        logger.info("\n🔍 Testing Hybrid Search...")
        
        if not hasattr(self, 'test_graphrag_result'):
            logger.warning("⚠️ No GraphRAG result available, skipping hybrid search test")
            self.test_results['hybrid_search_tests'] = {'success': False, 'error': 'No GraphRAG result'}
            return
        
        try:
            start_time = time.time()
            
            # Test various search queries
            test_queries = [
                "medical malpractice standard of care",
                "Judge Martinez court decision",
                "Texas Civil Practice Code expert testimony"
            ]
            
            search_results = {}
            
            for query in test_queries:
                result = await self.processor.hybrid_search(
                    query=query,
                    jurisdiction="tx",
                    practice_area="personal_injury",
                    search_types=['document_chunks', 'entities', 'relationships'],
                    top_k=5
                )
                
                search_results[query] = result
            
            search_time = time.time() - start_time
            
            # Analyze results
            total_results = sum(
                result.get('total_results', 0) for result in search_results.values()
            )
            
            self.test_results['hybrid_search_tests'] = {
                'queries_tested': len(test_queries),
                'total_results_found': total_results,
                'avg_results_per_query': total_results / len(test_queries) if test_queries else 0,
                'search_time_seconds': search_time,
                'avg_time_per_query': search_time / len(test_queries) if test_queries else 0,
                'search_results': search_results,
                'success': total_results > 0
            }
            
            logger.info(f"✅ Hybrid search test complete:")
            logger.info(f"   Queries tested: {len(test_queries)}")
            logger.info(f"   Total results: {total_results}")
            logger.info(f"   Avg time per query: {search_time / len(test_queries):.3f}s")
            
        except Exception as e:
            logger.error(f"❌ Hybrid search test failed: {e}")
            self.test_results['hybrid_search_tests'] = {'success': False, 'error': str(e)}
    
    async def _test_performance_analysis(self):
        """Test performance and cost analysis"""
        logger.info("\n📊 Testing Performance and Cost Analysis...")
        
        try:
            # Get comprehensive statistics
            stats = self.processor.get_processing_statistics()
            
            # Calculate performance metrics
            performance_metrics = {
                'total_processing_time': stats['embedding_stats']['processing_time'],
                'total_embeddings': stats['embedding_stats']['total_embeddings_generated'],
                'total_cost': stats['embedding_stats']['total_cost'],
                'embeddings_per_second': stats['performance_metrics']['embeddings_per_second'],
                'cost_per_embedding': stats['embedding_stats']['total_cost'] / max(stats['embedding_stats']['total_embeddings_generated'], 1),
                'memory_usage_mb': self._get_memory_usage(),
                'cache_efficiency': len(self.processor.embedding_cache) / max(stats['embedding_stats']['total_embeddings_generated'], 1)
            }
            
            self.test_results['performance_tests'] = {
                'comprehensive_stats': stats,
                'performance_metrics': performance_metrics,
                'cost_efficiency_rating': self._rate_cost_efficiency(performance_metrics['cost_per_embedding']),
                'performance_rating': self._rate_performance(performance_metrics['embeddings_per_second']),
                'success': True
            }
            
            logger.info(f"✅ Performance analysis complete:")
            logger.info(f"   Total cost: ${performance_metrics['total_cost']:.4f}")
            logger.info(f"   Cost per embedding: ${performance_metrics['cost_per_embedding']:.6f}")
            logger.info(f"   Embeddings/second: {performance_metrics['embeddings_per_second']:.2f}")
            logger.info(f"   Memory usage: {performance_metrics['memory_usage_mb']:.1f}MB")
            
        except Exception as e:
            logger.error(f"❌ Performance analysis failed: {e}")
            self.test_results['performance_tests'] = {'success': False, 'error': str(e)}
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def _rate_cost_efficiency(self, cost_per_embedding: float) -> str:
        """Rate cost efficiency"""
        if cost_per_embedding < 0.0001:
            return "EXCELLENT"
        elif cost_per_embedding < 0.0005:
            return "GOOD"
        elif cost_per_embedding < 0.001:
            return "ACCEPTABLE"
        else:
            return "NEEDS_OPTIMIZATION"
    
    def _rate_performance(self, embeddings_per_second: float) -> str:
        """Rate performance"""
        if embeddings_per_second > 10:
            return "EXCELLENT"
        elif embeddings_per_second > 5:
            return "GOOD"
        elif embeddings_per_second > 1:
            return "ACCEPTABLE"
        else:
            return "NEEDS_OPTIMIZATION"
    
    async def _generate_final_report(self) -> Dict[str, Any]:
        """Generate comprehensive final test report"""
        logger.info("\n📋 Generating Final Test Report...")
        
        # Calculate overall success rate
        successful_tests = sum(
            1 for test_results in self.test_results.values() 
            if isinstance(test_results, dict) and test_results.get('success', False)
        )
        total_tests = len(self.test_results)
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        # Generate recommendations
        recommendations = self._generate_recommendations()
        
        final_report = {
            'test_summary': {
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'success_rate_percent': success_rate,
                'test_timestamp': datetime.now().isoformat()
            },
            'detailed_results': self.test_results,
            'recommendations': recommendations,
            'next_steps': [
                "Deploy to staging environment for larger-scale testing",
                "Monitor cost and performance in production",
                "Implement automated regression testing",
                "Optimize embedding caching strategies",
                "Scale up GraphRAG entity extraction"
            ]
        }
        
        # Log summary
        logger.info("🎯 FINAL TEST REPORT SUMMARY:")
        logger.info(f"   Overall Success Rate: {success_rate:.1f}%")
        logger.info(f"   Successful Tests: {successful_tests}/{total_tests}")
        
        if self.test_results.get('performance_tests', {}).get('success'):
            perf_metrics = self.test_results['performance_tests']['performance_metrics']
            logger.info(f"   Total Cost: ${perf_metrics['total_cost']:.4f}")
            logger.info(f"   Cost Efficiency: {self.test_results['performance_tests']['cost_efficiency_rating']}")
            logger.info(f"   Performance Rating: {self.test_results['performance_tests']['performance_rating']}")
        
        logger.info("📋 Detailed results saved to enhanced_voyage_graphrag_test_results.json")
        
        return final_report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Chunking recommendations
        if self.test_results.get('chunking_tests', {}).get('success'):
            chunk_stats = self.test_results['chunking_tests']
            if chunk_stats.get('avg_chunk_size', 0) > 2000:
                recommendations.append("Consider reducing chunk size to stay within 2k token limit")
        
        # Embedding recommendations
        if self.test_results.get('embedding_tests', {}).get('success'):
            embedding_stats = self.test_results['embedding_tests']
            if embedding_stats.get('avg_time_per_embedding', 0) > 1.0:
                recommendations.append("Optimize embedding generation performance")
        
        # Performance recommendations
        if self.test_results.get('performance_tests', {}).get('success'):
            perf_metrics = self.test_results['performance_tests']['performance_metrics']
            if perf_metrics.get('cost_per_embedding', 0) > 0.001:
                recommendations.append("Implement more aggressive cost optimization")
            if perf_metrics.get('cache_efficiency', 0) < 0.5:
                recommendations.append("Improve embedding caching strategy")
        
        # Search recommendations
        if self.test_results.get('hybrid_search_tests', {}).get('success'):
            search_stats = self.test_results['hybrid_search_tests']
            if search_stats.get('avg_results_per_query', 0) < 1:
                recommendations.append("Improve search result relevance")
        
        return recommendations

async def main():
    """Main test execution function"""
    tester = EnhancedVoyageGraphRAGTester()
    
    try:
        # Run comprehensive tests
        results = await tester.run_comprehensive_tests()
        
        # Save results to file
        with open('enhanced_voyage_graphrag_test_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Return success based on results
        if results.get('test_summary', {}).get('success_rate_percent', 0) >= 80:
            logger.info("🎉 Test suite completed successfully!")
            return True
        else:
            logger.warning("⚠️ Test suite completed with some failures")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test suite failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)