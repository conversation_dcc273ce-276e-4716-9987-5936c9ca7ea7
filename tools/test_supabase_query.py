#!/usr/bin/env python3
"""
Test Supabase query format
"""

import sys
import os
from dotenv import load_dotenv

# Add processing to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'courtlistener', 'processing', 'src'))

from processing.storage.supabase_connector import SupabaseConnector

def test_supabase_query():
    """Test Supabase query format"""
    load_dotenv()
    
    supabase = SupabaseConnector()
    
    # First, check table structure
    schema_query = """
    SELECT column_name, data_type
    FROM information_schema.columns
    WHERE table_name = 'cases'
    ORDER BY ordinal_position
    """

    print("Checking table schema...")
    schema_result = supabase.execute_sql(schema_query)
    print(f"Schema result: {schema_result}")

    # Check what cases we have in the batch
    count_query = """
    SELECT COUNT(*) as total_cases,
           COUNT(CASE WHEN word_count > 0 THEN 1 END) as cases_with_words,
           AVG(word_count) as avg_word_count
    FROM cases
    WHERE batch_id = 'real_cl_verified_20250731_202652'
    """

    count_result = supabase.execute_sql(count_query)
    print(f"Batch statistics: {count_result}")

    # Simple query to check format - using available columns
    query = """
    SELECT id, case_name, case_name_full, court_id, source, judge_name, judge_metadata, word_count
    FROM cases
    WHERE batch_id = 'real_cl_verified_20250731_202652'
    LIMIT 3
    """
    
    result = supabase.execute_sql(query)
    
    print(f"Result type: {type(result)}")
    print(f"Result length: {len(result) if result else 0}")
    
    if result:
        print(f"Result is a list with {len(result)} items")
        if result:
            print(f"First item: {result[0]}")
            print(f"First item type: {type(result[0])}")
            if len(result) > 1:
                print(f"Second item: {result[1]}")
    else:
        print("No result returned")

if __name__ == "__main__":
    test_supabase_query()
